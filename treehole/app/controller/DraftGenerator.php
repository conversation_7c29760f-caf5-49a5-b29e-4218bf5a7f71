<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Config;
use think\Request;
use think\response\Json;
use think\App;

/**
 * 微信公众号草稿生成控制器
 * 功能：按照特定样式将数据库中的最新消息生成为微信公众号草稿
 */
class DraftGenerator extends BaseController
{
    // 微信公众号配置
    protected $appId = '';
    protected $appSecret = '';
    
    // 服务器地址配置
    protected $serverDomain = 'https://www.bjgaoxiaoshequ.store';
    
    // 消息配置
    protected $messageLimit = 10;
    protected $author = '月光下的温柔';
    protected $titleTemplate = '树洞最新消息 - {date}';
    protected $digest = '';
    
    // 样式配置
    protected $mainBgColor = '#f8f5f2';
    protected $titleBgGradient = ['#7D5A4F', '#A67F6D'];
    protected $buttonBgGradient = ['#7D5A4F', '#A67F6D'];
    protected $textColor = '#5a4a42';
    
    // 临时素材和永久素材缓存
    protected $mediaCache = [];
    protected $permanentMediaCache = [];
    
    // 封面图片配置
    protected $coverImagePath = '/uploads/cover3.jpg'; // 修改为新的封面图片
    
    // 日志文件配置
    protected $logFile = '';
    
    /**
     * 构造函数，加载配置
     */
    public function __construct(App $app)
    {
        parent::__construct($app);
        
        // 加载公众号配置，从wechat.php配置文件获取
        $this->appId = Config::get('wechat.official_account.app_id', '');
        $this->appSecret = Config::get('wechat.official_account.secret', '');
        
        // 加载封面图片配置 - 确保使用最新的封面图片
        $this->coverImagePath = '/uploads/cover2.jpg'; // 修改为新的封面图片
        
        // 设置日志文件路径
        $logDir = runtime_path() . 'logs/draft_generator/';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
        $this->logFile = $logDir . date('Y-m-d') . '.log';
        
        // 验证必要的配置是否存在
        if (empty($this->appId) || empty($this->appSecret)) {
            $this->log('微信公众号配置缺失：请在config/wechat.php中设置app_id和secret');
        }
    }
    
    /**
     * 记录日志到指定文件
     * 
     * @param string $message 日志消息
     * @param string $level 日志级别
     * @return void
     */
    private function log(string $message, string $level = 'INFO'): void
    {
        $time = date('Y-m-d H:i:s');
        $logMessage = "[{$time}] [{$level}] {$message}" . PHP_EOL;
        
        // 同时写入系统日志和专用日志文件
        error_log($message);
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
    
    /**
     * 记录信息级别日志
     * 
     * @param string $message 日志消息
     * @return void
     */
    private function logInfo(string $message): void
    {
        $this->log($message, 'INFO');
    }
    
    /**
     * 记录警告级别日志
     * 
     * @param string $message 日志消息
     * @return void
     */
    private function logWarning(string $message): void
    {
        $this->log($message, 'WARNING');
    }
    
    /**
     * 记录错误级别日志
     * 
     * @param string $message 日志消息
     * @return void
     */
    private function logError(string $message): void
    {
        $this->log($message, 'ERROR');
    }
    
    /**
     * 记录调试级别日志
     * 
     * @param string $message 日志消息
     * @return void
     */
    private function logDebug(string $message): void
    {
        $this->log($message, 'DEBUG');
    }
    
    /**
     * 生成公众号草稿并返回结果
     * 
     * @param Request $request
     * @return Json
     */
    public function generateDraft(Request $request): Json
    {
        try {
            // 清理旧日志
            $this->cleanOldLogs();
            
            $this->logInfo('开始生成草稿流程');
            
            // 1. 获取AccessToken
            $accessToken = $this->getAccessToken();
            if (empty($accessToken)) {
                return json(['code' => 500, 'msg' => '获取AccessToken失败']);
            }
            
            // 2. 上传封面图片 - 必须是永久素材，media_id才能用于封面
            $coverImageUrl = $request->param('cover_image', '');
            if (empty($coverImageUrl)) {
                // 使用默认封面图片
                $coverImageUrl = $this->serverDomain . $this->coverImagePath;
                $this->logInfo('使用服务器上最新的默认封面图片: ' . $coverImageUrl);
            }
            
            $this->logInfo('准备上传封面图片作为永久素材: ' . $coverImageUrl);
            
            // 上传封面图片作为永久素材
            $coverMediaId = $this->uploadImageAsPermanentMaterial($coverImageUrl, $accessToken);
            
            // 如果无法上传永久素材，尝试使用本地默认图片
            if (empty($coverMediaId)) {
                $this->logWarning('无法上传指定的封面图片作为永久素材，尝试使用本地默认图片');
                
                $defaultImagePaths = [
                    root_path() . 'public/images/default_cover.jpg',
                    root_path() . 'public/static/images/cover.jpg',
                    root_path() . 'public/uploads/cover3.jpg'
                ];
                
                foreach ($defaultImagePaths as $path) {
                    if (file_exists($path)) {
                        $this->logInfo('使用本地默认封面作为永久素材: ' . $path);
                        $coverMediaId = $this->uploadLocalImageAsPermanentMaterial($path, $accessToken);
                        if (!empty($coverMediaId)) {
                            break;
                        }
                    }
                }
                
                // 如果仍然无法获取封面图片，返回错误
                if (empty($coverMediaId)) {
                    $this->logError('无法获取有效的永久素材封面图片，草稿创建失败');
                    return json(['code' => 500, 'msg' => '无法获取有效的永久素材封面图片，草稿创建失败']);
                }
            }
            
            $this->logInfo('成功获取永久素材封面图片，media_id: ' . $coverMediaId);
            
            // 3. 上传公众号标题图片
            $titleImageUrl = $this->uploadTitleImage($accessToken);
            // 将URL传递给后续使用的方法
            $GLOBALS['titleImageUrl'] = $titleImageUrl;
            
            // 4. 获取数据库消息
            $limit = $request->param('limit', $this->messageLimit, 'intval');
            $messages = $this->getLatestMessages($limit);
            if (empty($messages)) {
                $this->logError('获取消息数据失败，未找到满足条件的消息');
                return json(['code' => 500, 'msg' => '获取消息数据失败，未找到符合条件的消息']);
            }
            
            $this->logInfo('成功获取' . count($messages) . '条最新消息');
            
            // 5. 正确处理：先上传作为永久素材（用于封面等），再上传作为图文消息内图片（获取URL）
            // 上传消息图片作为永久素材(对media_id的操作)
            $messages = $this->uploadMessagesImages($messages, $accessToken);
            
            // 上传消息图片作为图文消息内图片(获取URL)
            $messages = $this->uploadMessagesImagesForContent($messages, $accessToken);
            
            // 6. 上传小程序码
            $qrcodeUrl = $this->uploadQRCodeImage($accessToken);
            
            // 7. 生成文章内容
            $this->logInfo('开始生成文章内容');
            $content = $this->generateStyledContent($messages, $qrcodeUrl);
            $this->logInfo('文章内容生成完成，长度: ' . strlen($content));
            
            // 8. 创建草稿
            // 使用热度最高的消息内容作为标题，如果没有则使用默认模板
            if (!empty($GLOBALS['hotMessageTitle'])) {
                $title = "热帖：" . $GLOBALS['hotMessageTitle'];
                $this->logInfo('使用热度最高的消息作为标题: ' . $title);
            } else {
                $title = str_replace('{date}', date('Y-m-d'), $this->titleTemplate);
                $this->logInfo('使用默认标题模板: ' . $title);
            }
            
            // 尝试创建草稿
            $this->logInfo('开始尝试创建微信公众号草稿');
            $result = $this->createWechatDraft($title, $content, $coverMediaId, $this->author, $this->digest, $accessToken, $messages);
            
            // 如果创建失败，尝试切换为纯图片模式
            if (isset($result['error'])) {
                $this->logWarning('图文模式创建失败，尝试使用纯图片模式: ' . $result['error']);
                
                // 使用第一张有效的图片作为内容
                $validImageId = '';
                foreach ($messages as $message) {
                    if (!empty($message['processed_images']) && count($message['processed_images']) > 0) {
                        $validImageId = $message['processed_images'][0]['media_id'] ?? '';
                        if (!empty($validImageId)) {
                            break;
                        }
                    }
                }
                
                if (!empty($validImageId)) {
                    $this->logInfo('尝试使用图片类型创建草稿，使用图片media_id: ' . $validImageId);
                    $result = $this->createWechatDraftAsImageType($title, $validImageId, $coverMediaId, $accessToken);
                } else {
                    $this->logWarning('没有找到有效的图片，无法创建图片类型草稿');
                }
            }
            
            // 判断是否要自动发布
            if (isset($result['media_id'])) {
                $this->logInfo('草稿创建成功，media_id: ' . $result['media_id']);
                
                $autoPublish = $request->param('auto_publish', 0, 'intval');
                if ($autoPublish) {
                    $this->logInfo('自动发布选项开启，开始发布草稿');
                    $publishResult = $this->publishWechatDraft($result['media_id'], $accessToken);
                    
                    if (isset($publishResult['error'])) {
                        $this->logError('发布草稿失败: ' . $publishResult['error']);
                        return json(['code' => 500, 'msg' => '草稿生成成功，但发布失败: ' . $publishResult['error']]);
                    }
                    
                    $this->logInfo('草稿发布成功');
                    return json(['code' => 200, 'msg' => '草稿生成并发布成功', 'data' => $publishResult]);
                }
                
                return json(['code' => 200, 'msg' => '草稿生成成功', 'data' => $result]);
            }
            
            $this->logError('草稿创建失败: ' . ($result['error'] ?? '未知错误'));
            return json(['code' => 500, 'msg' => $result['error'] ?? '草稿创建失败，请查看日志']);
            
        } catch (\Exception $e) {
            $this->logError('生成草稿异常：' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return json(['code' => 500, 'msg' => '生成草稿失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 清理旧日志，避免日志文件过大
     */
    private function cleanOldLogs(): void
    {
        // 如果日志文件存在且超过5MB，进行清理
        if (file_exists($this->logFile) && filesize($this->logFile) > 5 * 1024 * 1024) {
            // 备份旧日志文件
            $backupFile = $this->logFile . '.bak.' . date('His');
            rename($this->logFile, $backupFile);
            
            // 创建新的日志文件
            $time = date('Y-m-d H:i:s');
            $newLogMessage = "[{$time}] [INFO] 日志文件已清理，旧日志已备份到 {$backupFile}" . PHP_EOL;
            file_put_contents($this->logFile, $newLogMessage);
        }
    }

    /**
     * 通过URL上传图片并存储为永久素材
     * 
     * @param string $imageUrl 图片URL
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回media_id，失败返回空字符串
     */
    private function uploadImageAsPermanentMaterial(string $imageUrl, string $accessToken): string
    {
        $this->logInfo('开始下载图片: ' . $imageUrl);
        
        // 从URL获取文件扩展名
        $pathInfo = pathinfo($imageUrl);
        $extension = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : 'jpg';
        
        // 确保扩展名是微信支持的
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            $extension = 'jpg';  // 默认使用jpg
        }
        
        // 下载图片到临时文件 - 确保包含正确的扩展名
        $tempFilePath = tempnam(sys_get_temp_dir(), 'wx_img');
        $tempFilePathWithExt = $tempFilePath . '.' . $extension;
        rename($tempFilePath, $tempFilePathWithExt); // 重命名临时文件以包含扩展名
        
        $this->logInfo('临时文件路径: ' . $tempFilePathWithExt);
        
        // 下载图片内容
        $imageData = @file_get_contents($imageUrl);
        if (!$imageData) {
            $this->logError('无法下载图片: ' . $imageUrl);
            return '';
        }
        
        // 保存图片到临时文件
        file_put_contents($tempFilePathWithExt, $imageData);
        $this->logInfo('成功下载图片到临时路径: ' . $tempFilePathWithExt);
        
        // 获取图片MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $tempFilePathWithExt);
        finfo_close($finfo);
        $this->logInfo('图片MIME类型: ' . $mimeType);
        
        // 确保MIME类型满足微信要求
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            $this->logError('不支持的MIME类型: ' . $mimeType . '，微信永久素材仅支持JPG/PNG/GIF');
            @unlink($tempFilePathWithExt);
            return null;
        }
        
        // 获取图片文件大小
        $fileSize = filesize($tempFilePathWithExt);
        $this->logInfo('原始图片大小: ' . $fileSize . ' 字节');
        
        // 检查文件大小是否超过微信限制 (2MB)
        if ($fileSize > 2 * 1024 * 1024) {
            $this->logError('图片大小超过2MB，微信永久素材大小限制为2MB');
            @unlink($tempFilePathWithExt);
            return null;
        }
        
        // 使用永久素材接口上传图片
        $mediaId = $this->uploadLocalImageAsPermanentMaterial($tempFilePathWithExt, $accessToken);
        
        // 删除临时文件
        @unlink($tempFilePathWithExt);
        $this->logInfo('已删除临时图片文件');
        
        return $mediaId;
    }
    
    /**
     * 将本地图片文件上传为微信永久素材
     * 
     * @param string $localPath 本地图片路径
     * @param string $accessToken 微信AccessToken
     * @return string|null 成功返回media_id，失败返回null
     */
    private function uploadLocalImageAsPermanentMaterial(string $localPath, string $accessToken): ?string
    {
        $this->logInfo('开始上传图片到微信服务器: ' . $localPath);
        
        // 判断是否是临时文件
        $isTemporaryFile = false;
        $originalPath = $localPath;
        
        // 检查文件是否存在
        if (!file_exists($localPath)) {
            $this->logError('文件不存在: ' . $localPath);
            return null;
        }
        
        // 获取图片MIME类型
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $localPath);
        finfo_close($finfo);
        
        // 确保MIME类型满足微信要求
        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($mimeType, $allowedMimeTypes)) {
            $this->logError('不支持的MIME类型: ' . $mimeType . '，微信永久素材仅支持JPG/PNG/GIF');
            return null;
        }
        
        // 检查文件大小
        $fileSize = filesize($localPath);
        $this->logInfo('准备上传图片，类型: ' . $mimeType . ', 大小: ' . $fileSize . ' 字节');
        
        // 检查文件大小是否超过微信限制 (2MB)
        if ($fileSize > 2 * 1024 * 1024) {
            $this->logError('图片大小超过2MB，微信永久素材大小限制为2MB');
            return null;
        }
        
        // 使用永久素材接口上传图片
        $url = "https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={$accessToken}&type=image";
        
        // 获取文件后缀名
        $fileExt = strtolower(pathinfo($localPath, PATHINFO_EXTENSION));
        if (empty($fileExt) || !in_array($fileExt, ['jpg', 'jpeg', 'png', 'gif'])) {
            // 根据MIME类型设置正确的扩展名
            if ($mimeType == 'image/jpeg') {
                $fileExt = 'jpg';
            } elseif ($mimeType == 'image/png') {
                $fileExt = 'png';
            } elseif ($mimeType == 'image/gif') {
                $fileExt = 'gif';
            } else {
                $fileExt = 'jpg';
            }
            
            // 创建一个新的临时文件，确保包含正确的扩展名
            $newPath = $localPath . '.' . $fileExt;
            copy($localPath, $newPath);
            $localPath = $newPath;
            $isTemporaryFile = true;
            $this->logInfo('添加扩展名后的文件路径: ' . $localPath);
        }
        
        $curl = curl_init();
        
        $data = [
            'media' => new \CURLFile($localPath, $mimeType, 'image.' . $fileExt)
        ];
        
        $this->logInfo('上传文件信息: 路径=' . $localPath . ', MIME类型=' . $mimeType . ', 文件名=image.' . $fileExt);
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $this->logInfo('图片上传响应状态码: ' . $httpCode);
        
        if ($httpCode !== 200 || curl_errno($curl)) {
            $error = curl_error($curl);
            curl_close($curl);
            $this->logError('图片上传失败: ' . $error);
            $this->logError('响应内容: ' . $response);
            
            // 删除临时创建的文件
            if ($isTemporaryFile && file_exists($localPath)) {
                @unlink($localPath);
                $this->logInfo('已删除临时创建的文件: ' . $localPath);
            }
            
            return null;
        }
        
        curl_close($curl);
        
        // 删除临时创建的文件
        if ($isTemporaryFile && file_exists($localPath)) {
            @unlink($localPath);
            $this->logInfo('已删除临时创建的文件: ' . $localPath);
        }
        
        // 解析响应内容
        $result = json_decode($response, true);
        if (isset($result['media_id'])) {
            $this->logInfo('图片上传成功，media_id: ' . $result['media_id']);
            return $result['media_id'];
        } else {
            $this->logError('上传响应: ' . $response);
            $errorMsg = isset($result['errmsg']) ? $result['errmsg'] : '未知错误';
            $errorCode = isset($result['errcode']) ? $result['errcode'] : 0;
            $this->logError('上传失败: 错误码=' . $errorCode . ', 错误信息=' . $errorMsg);
            return null;
        }
    }
    
    /**
     * 预览生成的内容（返回HTML页面）
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function previewContent(Request $request)
    {
        try {
            // 获取消息数据
            $limit = $request->param('limit', $this->messageLimit, 'intval');
            $messages = $this->getLatestMessages($limit);
            
            if (empty($messages)) {
                // 返回错误页面
                $html = '<!DOCTYPE html>
<html>
<head>
    <title>预览失败</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, "Microsoft YaHei", sans-serif;
            background-color: #f8f5f2;
            padding: 20px;
            color: #5a4a42;
            text-align: center;
        }
        .error-container {
            background: white;
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 20px;
        }
        p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(to right, #7D5A4F, #A67F6D);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>预览失败</h1>
        <p>获取消息数据失败：未找到符合条件的消息</p>
        <a href="/DraftGenerator/debugPage" class="btn">返回调试页面</a>
    </div>
</body>
</html>';
                return response($html);
            }
            
            // 为预览替换图片URL（不上传到微信服务器）
            foreach ($messages as &$message) {
                if (!empty($message['images'])) {
                    $imagesArray = json_decode($message['images'], true);
                    $message['processed_images'] = $imagesArray;
                } else {
                    $message['processed_images'] = [];
                }
            }
            
            // 生成HTML内容
            $content = $this->generatePreviewContent($messages);
            
            // 构建完整页面
            return response($content);
            
        } catch (\Exception $e) {
            // 返回错误页面
            $html = '<!DOCTYPE html>
<html>
<head>
    <title>预览失败</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, "Microsoft YaHei", sans-serif;
            background-color: #f8f5f2;
            padding: 20px;
            color: #5a4a42;
            text-align: center;
        }
        .error-container {
            background: white;
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc3545;
            font-size: 24px;
            margin-bottom: 20px;
        }
        p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        pre {
            text-align: left;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-size: 12px;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(to right, #7D5A4F, #A67F6D);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>预览失败</h1>
        <p>发生错误：</p>
        <pre>' . htmlspecialchars($e->getMessage()) . '</pre>
        <a href="/DraftGenerator/debugPage" class="btn">返回调试页面</a>
    </div>
</body>
</html>';
            return response($html);
        }
    }
    
    /**
     * 获取微信公众号的AccessToken
     * 
     * @return string 成功返回AccessToken，失败返回空字符串
     */
    private function getAccessToken(): string
    {
        try {
            // 构建获取AccessToken的URL
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";
            
            // 发送GET请求
            $response = file_get_contents($url);
            if (!$response) {
                $this->log('获取AccessToken失败：请求返回空');
                return '';
            }
            
            // 解析响应
            $result = json_decode($response, true);
            if (!$result || isset($result['errcode'])) {
                $this->log('获取AccessToken失败：' . json_encode($result));
                return '';
            }
            
            // 返回AccessToken
            return $result['access_token'] ?? '';
            
        } catch (\Exception $e) {
            $this->log('获取AccessToken异常：' . $e->getMessage());
            return '';
        }
    }
    
    /**
     * 获取热度排序的最新消息
     * 
     * @param int $limit 获取消息的数量限制
     * @return array 消息数组
     */
    private function getLatestMessages(int $limit = 15): array
    {
        $this->logInfo('开始查询热度最高的消息数据，limit: ' . $limit);
        
        try {
            // 测试数据库连接
            $this->testDbConnection();
            
            // 计算72小时前的时间戳
            $threeDay = time() - 72 * 3600; // 72小时
            
            // 构建查询SQL - 查询72小时内的消息，通过choose字段过滤已删除的消息
            $sql = "SELECT * FROM `message` WHERE `choose` < 100 AND `send_timestamp` > {$threeDay}";
            $this->logInfo('查询SQL: ' . $sql);
            
            // 执行查询
            $result = Db::query($sql);
            $this->logInfo('查询到 ' . count($result) . ' 条72小时内的消息');
            
            // 如果没有数据，返回空数组
            if (empty($result)) {
                return [];
            }
            
            // 处理每条消息，并计算热度分数
            foreach ($result as &$message) {
                // 格式化时间
                $timestamp = $message['send_timestamp'] ?? time();
                $message['formatted_time'] = date('m-d H:i', $timestamp);
                
                // 获取评论和回复数量
                $commentCount = Db::name('comment')
                    ->where('message_id', $message['id'])
                    ->where('is_deleted', 0)
                    ->count();
                $postCount = Db::name('post')
                    ->where('message_id', $message['id'])
                    ->where('is_deleted', 0)
                    ->count();
                $message['comment_count'] = $commentCount;
                $message['total_pinglun'] = $commentCount + $postCount;
                
                // 获取实际点赞数
                $message['total_likes'] = Db::name('message_like')
                    ->where('message_id', $message['id'])
                    ->count();
                
                // 处理用户名
                $message['username'] = $message['username'] ?? '匿名用户';
                
                // 确保图片字段存在且为正确的格式
                if (!isset($message['images']) || empty($message['images'])) {
                    $message['images'] = '[]';
                }
                
                // 计算热度分数
                // 公式：浏览量 + 评论数*50 + 回复数*30，再根据时间衰减
                $viewCount = intval($message['view'] ?? 0);
                $timeElapsed = time() - $timestamp; // 已经过去的时间（秒）
                $hoursPassed = $timeElapsed / 3600; // 转换为小时
                
                // 时间衰减系数 - 随着时间推移，热度降低
                // 这里使用一个简单的衰减公式：1 / (1 + 小时数/24)
                // 刚发布的是1，24小时后是0.5，48小时后是0.33，72小时后是0.25
                $timeDecay = 1 / (1 + $hoursPassed/24);
                
                // 计算最终热度分数
                $heatScore = ($viewCount + $commentCount * 50 + $postCount * 30) * $timeDecay;
                $message['heat_score'] = $heatScore;
            }
            
            // 按热度分数降序排序消息
            usort($result, function($a, $b) {
                return $b['heat_score'] <=> $a['heat_score'];
            });
            
            // 取前limit条消息
            $result = array_slice($result, 0, $limit);
            
            $this->logInfo('消息处理完成，返回 ' . count($result) . ' 条热度最高的消息');
            
            // 获取热度最高的消息标题，如果超过35个字符则截断添加省略号
            if (!empty($result) && isset($result[0]['content'])) {
                $topMessageContent = $result[0]['content'];
                $title = mb_strlen($topMessageContent, 'UTF-8') > 35 ? 
                    mb_substr($topMessageContent, 0, 35, 'UTF-8') . '...' : 
                    $topMessageContent;
                
                // 将热门消息标题保存到全局变量，供生成草稿标题使用
                $GLOBALS['hotMessageTitle'] = $title;
                $this->logInfo('热度最高消息的标题: ' . $title);
            }
            
            return $result;
            
        } catch (\Exception $e) {
            $this->logError('获取消息异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return [];
        }
    }
    
    /**
     * 测试数据库连接
     */
    private function testDbConnection()
    {
        try {
            $result = Db::query("SELECT 1");
            if ($result && isset($result[0]) && $result[0]['1'] === 1) {
                $this->logInfo('数据库连接测试成功');
            } else {
                $this->logWarning('数据库连接测试失败');
            }
        } catch (\Exception $e) {
            $this->logError('数据库连接测试异常: ' . $e->getMessage());
        }
    }
    
    /**
     * 上传消息中的所有图片
     * 
     * @param array $messages 消息数组
     * @param string $accessToken 微信AccessToken
     * @return array 处理后的消息数组
     */
    private function uploadMessagesImages(array $messages, string $accessToken): array
    {
        $this->logInfo('开始上传消息图片，共 ' . count($messages) . ' 条消息');
        
        foreach ($messages as &$message) {
            // 初始化处理后的图片数组
            $message['processed_images'] = [];
            $message['square_images'] = [];
            
            // 处理消息图片 - 先检查images字段的格式
            if (!empty($message['images'])) {
                $this->logInfo('处理消息ID:' . $message['id'] . ' 的图片信息: ' . $message['images']);
                
                // 尝试解析JSON格式的图片URL数组
                $imagesArray = json_decode($message['images'], true);
                
                // 确保解析后是数组并且不为空
                if (is_array($imagesArray) && !empty($imagesArray)) {
                    $this->logInfo('成功解析到 ' . count($imagesArray) . ' 张图片');
                    
                    // 处理每一张图片，但仅处理前两张（减少API调用）
                    $maxImages = min(2, count($imagesArray));
                    for ($i = 0; $i < $maxImages; $i++) {
                        $imageUrl = $imagesArray[$i];
                        $this->logInfo('处理第' . ($i+1) . '张图片: ' . $imageUrl);
                        
                        // 检查图片URL是否完整
                        if (strpos($imageUrl, 'http') !== 0) {
                            $imageUrl = $this->serverDomain . $imageUrl;
                            $this->logInfo('补全图片URL: ' . $imageUrl);
                        }
                        
                        // 检查图片是否存在本地文件版本
                        $localPath = $this->getLocalImagePath($imageUrl);
                        $originalMediaId = '';
                        $squareMediaId = '';
                        
                        if (!empty($localPath) && file_exists($localPath)) {
                            $this->logInfo('找到本地图片文件: ' . $localPath);
                            
                            // 上传原始图片为永久素材
                            $originalMediaId = $this->uploadLocalImageAsPermanentMaterial($localPath, $accessToken);
                            
                            // 裁剪图片为正方形并上传
                            $squarePath = $this->cropImageToSquare($localPath);
                            if (!empty($squarePath)) {
                                $squareMediaId = $this->uploadLocalImageAsPermanentMaterial($squarePath, $accessToken);
                                // 删除临时裁剪文件
                                if ($squarePath != $localPath && file_exists($squarePath)) {
                                    @unlink($squarePath);
                                }
                            }
                        } else {
                            // 通过URL上传图片为永久素材
                            $this->logInfo('未找到本地文件，尝试通过URL上传为永久素材');
                            
                            // 下载图片到临时文件
                            $tempFile = $this->downloadImage($imageUrl);
                            if (!empty($tempFile)) {
                                // 上传原始图片
                                $originalMediaId = $this->uploadLocalImageAsPermanentMaterial($tempFile, $accessToken);
                                
                                // 裁剪并上传方形图片
                                $squarePath = $this->cropImageToSquare($tempFile);
                                if (!empty($squarePath)) {
                                    $squareMediaId = $this->uploadLocalImageAsPermanentMaterial($squarePath, $accessToken);
                                    // 删除临时裁剪文件
                                    if ($squarePath != $tempFile && file_exists($squarePath)) {
                                        @unlink($squarePath);
                                    }
                                }
                                
                                // 删除原始临时文件
                                @unlink($tempFile);
                            }
                        }
                        
                        // 保存原始图片信息
                        if (!empty($originalMediaId)) {
                            $message['processed_images'][] = [
                                'url' => $imageUrl,
                                'media_id' => $originalMediaId
                            ];
                            $this->logInfo('原始图片上传为永久素材成功，media_id: ' . $originalMediaId);
                        } else {
                            $this->logWarning('原始图片上传为永久素材失败: ' . $imageUrl);
                        }
                        
                        // 保存方形图片信息
                        if (!empty($squareMediaId)) {
                            $message['square_images'][] = [
                                'url' => $imageUrl,
                                'media_id' => $squareMediaId
                            ];
                            $this->logInfo('方形图片上传为永久素材成功，media_id: ' . $squareMediaId);
                        } else {
                            $this->logWarning('方形图片上传为永久素材失败: ' . $imageUrl);
                        }
                    }
                } else {
                    $this->logWarning('无法解析消息图片JSON或数组为空: ' . $message['images']);
                }
            } else {
                $this->logInfo('消息ID:' . $message['id'] . ' 没有图片');
            }
            
            // 如果没有成功处理任何图片，记录警告
            if (empty($message['processed_images'])) {
                $this->logWarning('消息ID:' . $message['id'] . ' 没有成功处理任何图片');
            }
        }
        
        $this->logInfo('所有消息图片处理完成');
        return $messages;
    }
    
    /**
     * 根据URL获取可能的本地文件路径
     * 
     * @param string $url 图片URL
     * @return string|null 可能的本地路径，如果无法确定返回null
     */
    private function getLocalImagePath(string $url): ?string
    {
        // 检查URL是否包含文件路径部分
        if (strpos($url, '/file/') !== false) {
            // 提取file后面的路径
            $pattern = '/\/file\/(.*?)($|\?)/';
            if (preg_match($pattern, $url, $matches)) {
                $filePath = 'public/file/' . $matches[1];
                $this->logInfo('转换URL到本地路径: ' . dirname(__DIR__, 2) . '/' . $filePath);
                return dirname(__DIR__, 2) . '/' . $filePath;
            }
        }
        
        // 如果是uploads目录下的文件
        if (strpos($url, '/uploads/') !== false) {
            $pattern = '/\/uploads\/(.*?)($|\?)/';
            if (preg_match($pattern, $url, $matches)) {
                $filePath = 'public/uploads/' . $matches[1];
                $this->logInfo('转换URL到本地路径: ' . dirname(__DIR__, 2) . '/' . $filePath);
                return dirname(__DIR__, 2) . '/' . $filePath;
            }
        }
        
        return null;
    }
    
    /**
     * 通过URL上传图片到微信服务器
     * 
     * @param string $imageUrl 图片URL
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回media_id，失败返回空字符串
     */
    private function uploadImageByUrl(string $imageUrl, string $accessToken): string
    {
        // 检查缓存是否有此图片的media_id
        if (isset($this->mediaCache[$imageUrl])) {
            $this->logInfo('使用缓存的media_id: ' . $this->mediaCache[$imageUrl]);
            return $this->mediaCache[$imageUrl];
        }
        
        try {
            // 下载图片
            $imageTempPath = $this->downloadImage($imageUrl);
            if (empty($imageTempPath)) {
                $this->logError('下载图片失败: ' . $imageUrl);
                return '';
            }
            
            $this->logInfo('成功下载图片到临时路径: ' . $imageTempPath);
            
            // 检查图片是否存在
            if (!file_exists($imageTempPath) || filesize($imageTempPath) == 0) {
                $this->logError('下载的图片文件不存在或为空: ' . $imageTempPath);
                return '';
            }
            
            // 检查图片类型和大小
            $imageInfo = @getimagesize($imageTempPath);
            if (!$imageInfo) {
                $this->logError('无法获取图片信息，可能不是有效的图片文件: ' . $imageTempPath);
                return '';
            }
            
            $this->logInfo('图片类型: ' . $imageInfo['mime'] . ', 尺寸: ' . $imageInfo[0] . 'x' . $imageInfo[1]);
            
            // 检查图片大小，如果超过1MB则压缩
            $fileSize = filesize($imageTempPath);
            $this->logInfo('图片大小: ' . $fileSize . ' 字节');
            
            if ($fileSize > 1024 * 1024) { // 1MB
                $this->logInfo('图片大小超过1MB，进行压缩');
                $imageTempPath = $this->compressImage($imageTempPath, 1024 * 1024);
                if (empty($imageTempPath)) {
                    $this->logError('压缩图片失败');
                    return '';
                }
                $this->logInfo('压缩后图片大小: ' . filesize($imageTempPath) . ' 字节');
            }
            
            // 上传到微信服务器
            $mediaId = $this->uploadImageToWechat($imageTempPath, $accessToken);
            
            // 删除临时文件
            if (file_exists($imageTempPath)) {
                unlink($imageTempPath);
                $this->logInfo('已删除临时图片文件');
            }
            
            // 缓存并返回media_id
            if (!empty($mediaId)) {
                $this->mediaCache[$imageUrl] = $mediaId;
                $this->logInfo('成功上传图片，media_id: ' . $mediaId);
                return $mediaId;
            }
            
            $this->logError('上传图片失败，无法获取media_id');
            return '';
            
        } catch (\Exception $e) {
            $this->logError('通过URL上传图片异常：' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return '';
        }
    }
    
    /**
     * 下载图片到临时文件
     * 
     * @param string $url 图片URL
     * @return string 临时文件路径或空字符串
     */
    private function downloadImage(string $url): string
    {
        try {
            $this->logInfo('开始下载图片: ' . $url);
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'img_');
            if (!$tempFile) {
                $this->logError('创建临时文件失败');
                return '';
            }
            
            // 添加扩展名
            $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            if (empty($extension)) {
                $extension = 'jpg';
            }
            $tempFileWithExt = $tempFile . '.' . $extension;
            rename($tempFile, $tempFileWithExt);
            
            // 准备下载选项
            $options = [
                'http' => [
                    'method' => 'GET',
                    'header' => 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'timeout' => 30,  // 30秒超时
                ]
            ];
            $context = stream_context_create($options);
            
            // 尝试使用file_get_contents下载图片
            $imageData = @file_get_contents($url, false, $context);
            if ($imageData === false) {
                $this->logError('使用file_get_contents下载图片失败，尝试使用curl');
                
                // 尝试使用curl下载
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                $imageData = curl_exec($ch);
                $curlError = curl_error($ch);
                curl_close($ch);
                
                if (empty($imageData)) {
                    $this->logError('使用curl下载图片也失败: ' . $curlError);
                    return '';
                }
            }
            
            // 保存图片到临时文件
            if (file_put_contents($tempFileWithExt, $imageData) === false) {
                $this->logError('保存图片到临时文件失败');
                return '';
            }
            
            $this->logInfo('成功下载图片到: ' . $tempFileWithExt . ', 大小: ' . filesize($tempFileWithExt) . ' 字节');
            return $tempFileWithExt;
            
        } catch (\Exception $e) {
            $this->logError('下载图片异常：' . $e->getMessage());
            return '';
        }
    }
    
    /**
     * 上传图片到微信服务器
     * 
     * @param string $imagePath 图片路径
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回media_id，失败返回空字符串
     */
    private function uploadImageToWechat(string $imagePath, string $accessToken): string
    {
        try {
            $this->logInfo('开始上传图片到微信服务器: ' . $imagePath);
            
            // 检查图片是否存在
            if (!file_exists($imagePath)) {
                $this->logError('图片文件不存在: ' . $imagePath);
                return '';
            }
            
            // 获取文件信息
            $fileSize = filesize($imagePath);
            $imageInfo = @getimagesize($imagePath);
            
            if (!$imageInfo) {
                $this->logError('无法获取图片信息，可能不是有效的图片文件');
                return '';
            }
            
            $mimeType = $imageInfo['mime'];
            $this->logInfo('准备上传图片，类型: ' . $mimeType . ', 大小: ' . $fileSize . ' 字节');
            
            // 构建上传URL - 使用临时素材接口
            $url = "https://api.weixin.qq.com/cgi-bin/media/upload?access_token={$accessToken}&type=image";
            
            // 准备要上传的文件
            $data = [];
            $data['media'] = new \CURLFile($imagePath, $mimeType, basename($imagePath));
            
            // 发送POST请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 延长超时时间
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $this->logInfo('图片上传响应状态码: ' . $httpCode);
            
            if ($error) {
                $this->logError('上传图片到微信服务器CURL错误：' . $error);
                return '';
            }
            
            // 解析响应
            $result = json_decode($response, true);
            if (!$result) {
                $this->logError('上传图片响应解析失败：' . $response);
                return '';
            }
            
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                $this->logError('上传图片到微信服务器失败：' . json_encode($result, JSON_UNESCAPED_UNICODE));
                return '';
            }
            
            // 返回media_id
            $mediaId = $result['media_id'] ?? '';
            if (!empty($mediaId)) {
                $this->logInfo('图片上传成功，media_id: ' . $mediaId);
                return $mediaId;
            }
            
            $this->logError('上传成功但未返回media_id: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
            return '';
            
        } catch (\Exception $e) {
            $this->logError('上传图片到微信服务器异常：' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return '';
        }
    }
    
    /**
     * 上传小程序码图片
     * 
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回小程序码的URL，失败返回空字符串
     */
    private function uploadQRCodeImage(string $accessToken): string
    {
        // 更正小程序码图片路径
        $qrcodePath = root_path() . 'public/uploads/小程序码.jpg';
        
        // 检查小程序码是否存在，如果不存在则尝试备用图片
        if (!file_exists($qrcodePath)) {
            $this->logWarning('小程序码不存在：' . $qrcodePath);
            
            // 尝试其他可能的位置
            $alternatePaths = [
                root_path() . 'public/images/xiaochengxu_code.jpg', // 保留原路径作为备用
                root_path() . 'public/小程序码.jpg',
                root_path() . 'public/static/images/小程序码.jpg',
                root_path() . 'public/uploads/xiaochengxu_code.jpg'
            ];
            
            foreach ($alternatePaths as $path) {
                if (file_exists($path)) {
                    $this->logInfo('使用备用小程序码: ' . $path);
                    $qrcodePath = $path;
                    break;
                }
            }
            
            // 如果所有备用路径都不存在，则不包含小程序码
            if (!file_exists($qrcodePath)) {
                $this->logWarning('所有备用小程序码路径都不存在，将不添加小程序码');
                return '';
            }
        }
        
        // 使用uploadImageForNewsContent上传小程序码图片，获取URL
        $imageUrl = $this->uploadImageForNewsContent($qrcodePath, $accessToken);
        if (empty($imageUrl)) {
            $this->logError('上传小程序码失败');
        } else {
            $this->logInfo('成功上传小程序码，URL: ' . $imageUrl);
        }
        
        return $imageUrl;
    }
    
    /**
     * 上传图片到微信服务器(用于图文消息正文)
     * 
     * @param string $imagePath 图片路径
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回图片URL，失败返回空字符串
     */
    private function uploadImageForNewsContent(string $imagePath, string $accessToken): string
    {
        try {
            // 设置内存和超时限制
            $this->prepareImageProcessing();
            $this->logInfo('开始上传图文内容图片: ' . $imagePath);
            
            // 检查文件是否存在
            if (!file_exists($imagePath)) {
                $this->logError('图片文件不存在: ' . $imagePath);
                return '';
            }
            
            // 获取图片MIME类型
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $imagePath);
            finfo_close($finfo);
            
            // 检查MIME类型
            $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!in_array($mimeType, $allowedMimeTypes)) {
                $this->logError('不支持的图片类型: ' . $mimeType);
                return '';
            }
            
            // 检查文件大小
            $fileSize = filesize($imagePath);
            $this->logInfo('图片大小: ' . $fileSize . ' 字节');
            
            // 对大图片进行预处理 - 超过1MB的图片先压缩
            $processedPath = $imagePath;
            $needCleanup = false;
            
            if ($fileSize > 1024 * 1024) { // 1MB
                $this->logInfo('图片大于1MB，进行压缩处理');
                
                // 尝试使用Imagick压缩(如果可用)
                if (extension_loaded('imagick')) {
                    try {
                        $this->logInfo('使用Imagick压缩大图片');
                        $imagick = new \Imagick($imagePath);
                        
                        // 如果图片尺寸过大，先缩小
                        $width = $imagick->getImageWidth();
                        $height = $imagick->getImageHeight();
                        if ($width > 1200 || $height > 1200) {
                            $ratio = $width / $height;
                            if ($ratio > 1) {
                                $newWidth = min(1200, $width);
                                $newHeight = $newWidth / $ratio;
                            } else {
                                $newHeight = min(1200, $height);
                                $newWidth = $newHeight * $ratio;
                            }
                            $imagick->resizeImage($newWidth, $newHeight, \Imagick::FILTER_LANCZOS, 1);
                        }
                        
                        // 设置压缩质量
                        $imagick->setImageCompressionQuality(75);
                        
                        // 保存结果
                        $processedPath = tempnam(sys_get_temp_dir(), 'news_') . '.' . pathinfo($imagePath, PATHINFO_EXTENSION);
                        $imagick->writeImage($processedPath);
                        $imagick->clear();
                        $imagick->destroy();
                        
                        $needCleanup = true;
                        $this->logInfo('Imagick压缩成功: ' . $processedPath . ', 大小: ' . filesize($processedPath) . ' 字节');
                    } catch (\Exception $e) {
                        $this->logWarning('Imagick压缩失败: ' . $e->getMessage() . '，尝试GD压缩');
                    }
                }
                
                // 如果Imagick失败或不可用，尝试GD库压缩
                if ($processedPath === $imagePath || !file_exists($processedPath)) {
                    $processedPath = $this->compressImageFile($imagePath, 1024 * 1024);
                    if (!empty($processedPath)) {
                        $needCleanup = true;
                        $this->logInfo('GD压缩成功: ' . $processedPath . ', 大小: ' . filesize($processedPath) . ' 字节');
                    } else {
                        // 压缩失败，使用原图，但可能会导致上传失败或缓慢
                        $processedPath = $imagePath;
                        $this->logWarning('压缩失败，使用原图上传，可能会导致上传缓慢');
                    }
                }
            }
            
            // 构建上传URL - 使用正确的图文消息图片上传接口
            $url = "https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token={$accessToken}";
            
            // 准备要上传的文件
            $data = [];
            $data['media'] = new \CURLFile($processedPath, $mimeType, basename($processedPath));
            
            // 发送POST请求，添加超时设置
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30秒超时
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10秒连接超时
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            // 清理临时文件
            if ($needCleanup && $processedPath !== $imagePath && file_exists($processedPath)) {
                @unlink($processedPath);
                $this->logInfo('已删除临时处理图片文件');
            }
            
            $this->logInfo('图片上传响应状态码: ' . $httpCode);
            
            if ($error) {
                $this->logError('上传图片CURL错误：' . $error);
                return '';
            }
            
            // 解析响应
            $result = json_decode($response, true);
            if (!$result) {
                $this->logError('上传图片响应解析失败：' . $response);
                return '';
            }
            
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                $this->logError('上传图片失败：' . json_encode($result, JSON_UNESCAPED_UNICODE));
                return '';
            }
            
            // 返回图片URL
            $imageUrl = $result['url'] ?? '';
            if (!empty($imageUrl)) {
                $this->logInfo('图片上传成功，URL: ' . $imageUrl);
                return $imageUrl;
            }
            
            $this->logError('上传图片成功但未返回URL: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
            return '';
            
        } catch (\Exception $e) {
            $this->logError('上传图片异常：' . $e->getMessage());
            return '';
        }
    }
    
    /**
     * 上传消息中的所有图片(用于图文消息正文)
     * 
     * @param array $messages 消息数组
     * @param string $accessToken 微信AccessToken
     * @return array 处理后的消息数组
     */
    private function uploadMessagesImagesForContent(array $messages, string $accessToken): array
    {
        $this->logInfo('开始上传消息图片(用于图文消息正文)，共 ' . count($messages) . ' 条消息');
        
        foreach ($messages as &$message) {
            // 初始化处理后的图片数组
            $message['content_images'] = [];
            $message['square_content_images'] = [];
            
            // 处理消息图片 - 先检查images字段的格式
            if (!empty($message['images'])) {
                $this->logInfo('处理消息ID:' . $message['id'] . ' 的图片信息: ' . $message['images']);
                
                // 尝试解析JSON格式的图片URL数组
                $imagesArray = json_decode($message['images'], true);
                
                // 确保解析后是数组并且不为空
                if (is_array($imagesArray) && !empty($imagesArray)) {
                    $this->logInfo('成功解析到 ' . count($imagesArray) . ' 张图片');
                    
                    // 处理每一张图片，但仅处理前两张（减少API调用）
                    $maxImages = min(2, count($imagesArray));
                    for ($i = 0; $i < $maxImages; $i++) {
                        $imageUrl = $imagesArray[$i];
                        $this->logInfo('处理第' . ($i+1) . '张图片: ' . $imageUrl);
                        
                        // 检查图片URL是否完整
                        if (strpos($imageUrl, 'http') !== 0) {
                            $imageUrl = $this->serverDomain . $imageUrl;
                            $this->logInfo('补全图片URL: ' . $imageUrl);
                        }
                        
                        $originalUploadedUrl = '';
                        $squareUploadedUrl = '';
                        
                        // 检查图片是否存在本地文件版本
                        $localPath = $this->getLocalImagePath($imageUrl);
                        if (!empty($localPath) && file_exists($localPath)) {
                            $this->logInfo('找到本地图片文件: ' . $localPath);
                            
                            // 上传原始图片
                            $originalUploadedUrl = $this->uploadImageForNewsContent($localPath, $accessToken);
                            
                            // 裁剪并上传方形图片
                            $squarePath = $this->cropImageToSquare($localPath);
                            if (!empty($squarePath)) {
                                $squareUploadedUrl = $this->uploadImageForNewsContent($squarePath, $accessToken);
                                // 删除临时裁剪文件
                                if ($squarePath != $localPath && file_exists($squarePath)) {
                                    @unlink($squarePath);
                                }
                            }
                        } else {
                            // 通过URL下载图片后上传
                            $this->logInfo('未找到本地文件，尝试通过URL下载后上传');
                            $tempFile = $this->downloadImage($imageUrl);
                            if (!empty($tempFile)) {
                                // 上传原始图片
                                $originalUploadedUrl = $this->uploadImageForNewsContent($tempFile, $accessToken);
                                
                                // 裁剪并上传方形图片
                                $squarePath = $this->cropImageToSquare($tempFile);
                                if (!empty($squarePath)) {
                                    $squareUploadedUrl = $this->uploadImageForNewsContent($squarePath, $accessToken);
                                    // 删除临时裁剪文件
                                    if ($squarePath != $tempFile && file_exists($squarePath)) {
                                        @unlink($squarePath);
                                    }
                                }
                                
                                // 删除原始临时文件
                                @unlink($tempFile);
                            }
                        }
                        
                        // 保存原始图片信息
                        if (!empty($originalUploadedUrl)) {
                            $message['content_images'][] = [
                                'original_url' => $imageUrl,
                                'url' => $originalUploadedUrl
                            ];
                            $this->logInfo('原始图片上传成功，URL: ' . $originalUploadedUrl);
                        } else {
                            $this->logWarning('原始图片上传失败: ' . $imageUrl);
                        }
                        
                        // 保存方形图片信息
                        if (!empty($squareUploadedUrl)) {
                            $message['square_content_images'][] = [
                                'original_url' => $imageUrl,
                                'url' => $squareUploadedUrl
                            ];
                            $this->logInfo('方形图片上传成功，URL: ' . $squareUploadedUrl);
                        } else {
                            $this->logWarning('方形图片上传失败: ' . $imageUrl);
                        }
                    }
                } else {
                    $this->logWarning('无法解析消息图片JSON或数组为空: ' . $message['images']);
                }
            } else {
                $this->logInfo('消息ID:' . $message['id'] . ' 没有图片');
            }
        }
        
        $this->logInfo('所有消息图片处理完成');
        return $messages;
    }
    
    /**
     * 生成具有特定样式的文章内容
     * 
     * @param array $messages 消息数组
     * @param string $qrcodeUrl 小程序码的URL
     * @return string HTML格式的文章内容
     */
    private function generateStyledContent(array $messages, string $qrcodeUrl = ''): string
    {
        // 使用微信公众号支持的简单HTML结构
        $html = '';
        
        // 设置整体背景色 - 使用浅棕色背景
        $html .= '<section style="background-color: #f8f5f2; padding: 1px 0 10px;">';
        
        // 标题部分 - 使用公众号标题图片
        $html .= '<section style="text-align: center; margin: 20px 0;">';
        // 使用上传的标题图片URL，添加小程序跳转链接
        if (!empty($GLOBALS['titleImageUrl'])) {
            // 添加小程序跳转链接，修正为正确的小程序页面路径
            $html .= '<a data-miniprogram-appid="wx13d6e0dee303467f" data-miniprogram-path="pages/fold1/home/<USER>">';
            $html .= '<img src="' . $GLOBALS['titleImageUrl'] . '" style="max-width: 90%; border-radius: 8px;" />';
            $html .= '</a>';
        }
        $html .= '</section>';
        
        // 消息列表
        $counter = 1; // 用于记录显示的消息编号
        foreach ($messages as $message) {
            $html .= $this->formatMessageItem($message, $counter);
            $counter++;
        }
        
        // 底部引导区域 - 使用上传到微信的小程序码
        $html .= '<section style="margin: 20px 15px; text-align: center; background: #fff; padding: 20px; border-radius: 10px; box-shadow: 0 1px 4px rgba(0,0,0,0.05);">';
        $html .= '<p style="font-size: 15px; color: #5D4037; margin-bottom: 12px; font-weight: bold;">扫码进入树洞消息推送群</p>';
        $html .= '<p><br/></p>';
        
        // 使用微信服务器上的小程序码图片
        if (!empty($qrcodeUrl)) {
            $html .= '<p style="text-align:center">';
            $html .= '<img src="' . $qrcodeUrl . '" style="width: 140px; height: 140px;" />';
            $html .= '</p>';
        } else {
            // 备用方案，使用原始URL
            $html .= '<p style="text-align:center"><img src="https://www.bjgaoxiaoshequ.store/uploads/小程序码.jpg" style="width:140px;" /></p>';
        }
        
        $html .= '<p style="font-size: 13px; color: #8D6E63; margin-top: 12px;">浏览更多内容，参与校园互动</p>';
        $html .= '</section>';
        
        // 页脚
        $html .= '<section style="margin: 0 15px 15px; background: #fff; border-radius: 10px; padding: 12px; text-align: center; color: #8D6E63; font-size: 12px; box-shadow: 0 1px 4px rgba(0,0,0,0.05);">';
        $html .= '</section>';
        
        // 结束整体背景区域
        $html .= '</section>';
        
        return $html;
    }
    
    /**
     * 格式化单个消息项，使用方形图片展示，点击可查看原图
     * @param array $message 消息数据
     * @param int $index 序号
     * @return string 格式化后的HTML
     */
    private function formatMessageItem(array $message, int $index): string
    {
        // 格式化发布时间 - 月日时分
        $time = isset($message['formatted_time']) ? $message['formatted_time'] : '';
        
        // 处理月份开头的0，例如将05月改为5月
        if (!empty($time)) {
            $time = preg_replace('/^0(\d+)月/', '$1月', $time);
        }
        
        // 处理内容，防止XSS
        $content = htmlspecialchars($message['content']);
        
        // 限制文本内容长度，超过60个字符显示省略号
        if (mb_strlen($content, 'UTF-8') > 60) {
            $content = mb_substr($content, 0, 60, 'UTF-8') . '...';
        }
        
        // 小程序AppID和页面路径
        $appId = 'wx13d6e0dee303467f';
        $path = 'packageEmoji/pages/messageDetail/messageDetail?id=' . $message['id'];
        
        // 用户名和标题，如果有头衔则显示
        $titleName = isset($message['titlename']) && !empty($message['titlename']) ? $message['titlename'] : '';
        if (empty($titleName) && isset($message['user_title']) && !empty($message['user_title'])) {
            $titleName = $message['user_title'];
        }
        
        // 评论数
        $commentCount = isset($message['total_pinglun']) ? $message['total_pinglun'] : 0;
        if ($commentCount == 0 && isset($message['comment_count'])) {
            $commentCount = $message['comment_count'];
        }
        
        // 点赞数
        $likeCount = isset($message['total_likes']) ? $message['total_likes'] : 0;
        
        // 使用微信公众号支持的简单格式，两侧露出背景
        $html = '<section style="margin: 0 15px 16px; background: #fff; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 6px rgba(0,0,0,0.08);">';
        
        // 用户信息区域 - 减少内边距使更紧凑
        $html .= '<section style="padding: 8px 15px; border-bottom: 1px solid #f0f0f0; display: flex; align-items: center; justify-content: space-between;">';
        
        // 用户名和头衔 (去掉头像) - 减小字体大小到12px
        $html .= '<div style="max-width: 75%;">';
        $html .= '<strong style="font-size: 12px; display: inline-block;">' . $message['username'] . '</strong>';
        
        // 如果有头衔，放在用户名旁边 - 减小字体大小
        if (!empty($titleName)) {
            $html .= ' <span style="color: #ff9800; font-size: 10px; margin-left: 5px; border: 1px solid #ff9800; border-radius: 10px; padding: 0 6px; display: inline-block; vertical-align: middle;">' . $titleName . '</span>';
        }
        $html .= '</div>';
        
        // 右上角显示日期时间，确保在微信环境中也正确显示
        $html .= '<span style="color: #A67F6D; font-size: 12px; text-align: right; min-width: 60px; display: inline-block;">' . $time . '</span>';
        
        $html .= '</section>';
        
        // 内容区域 - 优化布局，减少内边距
        $html .= '<section style="padding: 10px 15px 12px; word-break: break-all;">';
        $html .= '<p style="margin: 0; font-size: 15px; line-height: 1.6; color: #333;">' . $content . '</p>';
        
        // 处理图片 - 确保图片容器是完全的正方形，并且任何比例的图片都显示为方形
        $hasImages = false;
        $imageCount = 0;
        $squareImageUrls = [];
        $originalImageUrls = [];
        $squareMediaIds = [];
        $originalMediaIds = [];
        
        // 获取图片信息 - 优先使用方形裁剪图片显示，点击时链接到原图
        if (!empty($message['square_content_images']) && !empty($message['content_images']) && 
            is_array($message['square_content_images']) && is_array($message['content_images'])) {
            $hasImages = true;
            
            // 收集方形图片和原始图片URL
            foreach ($message['square_content_images'] as $index => $square) {
                if (!empty($square['url']) && isset($message['content_images'][$index]) && !empty($message['content_images'][$index]['url'])) {
                    $squareImageUrls[] = $square['url'];
                    $originalImageUrls[] = $message['content_images'][$index]['url'];
                }
            }
            $imageCount = count($squareImageUrls);
        }
        elseif (!empty($message['square_images']) && !empty($message['processed_images']) && 
                is_array($message['square_images']) && is_array($message['processed_images'])) {
            $hasImages = true;
            
            // 收集方形图片和原始图片media_id
            foreach ($message['square_images'] as $index => $square) {
                if (!empty($square['media_id']) && isset($message['processed_images'][$index]) && !empty($message['processed_images'][$index]['media_id'])) {
                    $squareMediaIds[] = $square['media_id'];
                    $originalMediaIds[] = $message['processed_images'][$index]['media_id'];
                }
            }
            $imageCount = count($squareMediaIds);
        }
        elseif (!empty($message['processed_images']) && is_array($message['processed_images'])) {
            // 兼容旧版本，仅使用原图
            $hasImages = true;
            foreach ($message['processed_images'] as $img) {
                if (!empty($img['media_id'])) {
                    $originalMediaIds[] = $img['media_id'];
                }
            }
            $imageCount = count($originalMediaIds);
            $squareMediaIds = $originalMediaIds; // 没有方形图，使用原图
        }
        elseif (!empty($message['content_images']) && is_array($message['content_images'])) {
            // 兼容旧版本，仅使用原图URL
            $hasImages = true;
            foreach ($message['content_images'] as $img) {
                if (!empty($img['url'])) {
                    $originalImageUrls[] = $img['url'];
                }
            }
            $imageCount = count($originalImageUrls);
            $squareImageUrls = $originalImageUrls; // 没有方形图，使用原图
        }
        elseif (!empty($message['images'])) {
            // 尝试解析JSON格式的图片字段 (兼容最原始的方式)
            $imagesArray = json_decode($message['images'], true);
            if (is_array($imagesArray) && !empty($imagesArray)) {
                $hasImages = true;
                $originalImageUrls = $imagesArray;
                $squareImageUrls = $imagesArray; // 没有方形图，使用原图
                $imageCount = count($originalImageUrls);
            }
        }
        
        // 如果有图片，显示图片网格
        if ($hasImages && $imageCount > 0) {
            $html .= '<div style="margin-top: 10px;">';
            
            // 限制显示最多3张图片，即使实际有更多
            $displayCount = min($imageCount, 3);
            
            // 根据图片数量决定布局
            if ($displayCount == 1) {
                // 单图布局 - 使用方形图片显示，点击查看原图
                if (!empty($squareImageUrls) && !empty($originalImageUrls)) {
                    // 使用图片URL方式 (用于图文消息内容)
                    $html .= '<div style="width: 70%; height: 0; padding-bottom: 70%; margin: 0 auto; position: relative; overflow: hidden; border-radius: 8px; background-color: #f2f2f2;">';
                    $html .= '<a href="' . $originalImageUrls[0] . '" target="_blank">';
                    $html .= '<img src="' . $squareImageUrls[0] . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" />';
                    $html .= '</a>';
                    $html .= '</div>';
                } elseif (!empty($squareMediaIds) && !empty($originalMediaIds)) {
                    // 使用media_id方式 (用于引用永久素材)
                    $html .= '<div style="width: 70%; height: 0; padding-bottom: 70%; margin: 0 auto; position: relative; overflow: hidden; border-radius: 8px; background-color: #f2f2f2;">';
                    // 显示方形图片，但在微信文章中无法直接链接查看原图，所以仅使用方形图片展示
                    $html .= '<img src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==" data-media-id="' . $squareMediaIds[0] . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; visibility: visible !important;" />';
                    $html .= '</div>';
                }
            } else {
                // 多图网格布局 - 使用方形图片，每个点击后查看对应原图
                $html .= '<table style="width: 100%; border-collapse: separate; border-spacing: 3px; margin: 0; padding: 0;">';
                $html .= '<tr>';
                
                // 计算每个图片容器的宽度百分比
                $cellWidth = (100 / 3) - 1.5; // 留一点间距
                
                for ($i = 0; $i < $displayCount; $i++) {
                    $html .= '<td style="width: ' . $cellWidth . '%; padding: 0; vertical-align: top;">';
                    
                    // 创建正方形容器 - 强制使用padding-bottom确保正方形
                    $html .= '<div style="width: 100%; height: 0; padding-bottom: 100%; position: relative; overflow: hidden; border-radius: 6px; background-color: #f2f2f2;">';
                    
                    // 根据是否有URL或media_id决定使用哪种方式显示图片
                    if (!empty($squareImageUrls) && isset($squareImageUrls[$i]) && !empty($originalImageUrls) && isset($originalImageUrls[$i])) {
                        // 使用URL方式，点击查看原图
                        $html .= '<a href="' . $originalImageUrls[$i] . '" target="_blank">';
                        $html .= '<img src="' . $squareImageUrls[$i] . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" />';
                        $html .= '</a>';
                    } elseif (!empty($squareMediaIds) && isset($squareMediaIds[$i])) {
                        // 使用media_id方式，仅显示方形图片
                        $html .= '<img src="data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQImWNgYGBgAAAABQABh6FO1AAAAABJRU5ErkJggg==" data-media-id="' . $squareMediaIds[$i] . '" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; visibility: visible !important;" />';
                    }
                    
                    $html .= '</div>';
                    $html .= '</td>';
                }
                
                // 如果显示的图片少于3张，填充空白单元格保持布局一致
                for ($i = $displayCount; $i < 3; $i++) {
                    $html .= '<td style="width: ' . $cellWidth . '%;"></td>';
                }
                
                $html .= '</tr>';
                $html .= '</table>';
                
                // 如果图片总数超过3张，显示总数信息
                if ($imageCount > 3) {
                    $html .= '<p style="text-align: right; color: #999; font-size: 12px; margin: 3px 0 0;">共' . $imageCount . '张</p>';
                }
            }
            
            $html .= '</div>';
        }
        
        $html .= '</section>';
        
        // 底部信息栏 - 更紧凑的布局，减小内边距
        $html .= '<section style="padding: 8px 15px; background: #f9f7f6; border-top: 1px solid #eee; font-size: 12px; color: #888; display: flex; justify-content: space-between; align-items: center;">';
        
        // 左侧基本信息
        $html .= '<div style="flex: 1;">';
        $html .= '<span style="margin-right: 10px;"><i style="font-size: 12px;">💬</i> ' . $commentCount . '</span>';
        $html .= '<span style="margin-right: 10px;"><i style="font-size: 12px;">👍</i> ' . $likeCount . '</span>';
        $html .= '</div>';
        
        // 右侧按钮 - 更小的按钮
        $html .= '<div>';
        $html .= '<a class="weapp_text_link" data-miniprogram-appid="' . $appId . '" ';
        $html .= 'data-miniprogram-path="' . $path . '" ';
        $html .= 'style="display: inline-block; color: #fff; background: linear-gradient(to right, #7D5A4F, #A67F6D); padding: 1px 8px; border-radius: 10px; font-size: 10px; text-decoration: none; line-height: 1.5;">';
        $html .= '查看详情</a>';
        $html .= '</div>';
        
        $html .= '</section>';
        $html .= '</section>';
        
        return $html;
    }
    
    /**
     * 为预览生成内容HTML（使用原始图片URL而非微信media_id）
     * 
     * @param array $messages 消息数组
     * @return string HTML格式的预览内容
     */
    private function generatePreviewContent(array $messages): string
    {
        // 开始构建HTML内容（与真实内容样式相同，但使用原始图片）
        $html = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>树洞最新消息预览</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: {$this->mainBgColor};
            color: {$this->textColor};
            margin: 0;
            padding: 15px;
        }
        .title-container {
            background: linear-gradient(to right, {$this->titleBgGradient[0]}, {$this->titleBgGradient[1]});
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .title {
            font-size: 22px;
            margin: 0;
            font-weight: bold;
        }
        .subtitle {
            font-size: 14px;
            margin: 8px 0 0;
            opacity: 0.9;
        }
        .preview-notice {
            background: #ffeb3b;
            color: #333;
            padding: 10px;
            margin-bottom: 20px;
            text-align: center;
            border-radius: 5px;
        }
        .intro-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .message-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            background-color: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #aaa;
        }
        .username {
            font-weight: bold;
            font-size: 16px;
        }
        .user-title {
            font-size: 12px;
            color: #888;
            margin-left: 8px;
        }
        .message-content {
            font-size: 15px;
            line-height: 1.5;
            margin-bottom: 15px;
            word-break: break-all;
        }
        .images-container {
            margin-bottom: 10px;
        }
        .images-container img {
            max-width: 100%;
            border-radius: 5px;
            margin-bottom: 5px;
        }
        .single-image {
            text-align: center;
        }
        .multi-images {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
        }
        .image-item img {
            width: 100%;
            height: 80px;
            object-fit: cover;
        }
        .more-images {
            position: relative;
        }
        .more-images::after {
            content: '+'attr(data-count)'张';
            position: absolute;
            bottom: 5px;
            right: 5px;
            background: rgba(0,0,0,0.6);
            color: white;
            padding: 2px 8px;
            font-size: 12px;
            border-radius: 10px;
        }
        .message-footer {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #999;
            border-top: 1px solid #f0f0f0;
            padding-top: 10px;
        }
        .message-stats {
            flex: 1;
        }
        .message-time {
            text-align: right;
        }
        .view-detail {
            display: inline-block;
            background: linear-gradient(to right, {$this->buttonBgGradient[0]}, {$this->buttonBgGradient[1]});
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 13px;
            text-decoration: none;
            margin-top: 10px;
        }
        .qrcode-container {
            text-align: center;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        .qrcode-container img {
            width: 180px;
            height: 180px;
            margin-bottom: 10px;
        }
        .qrcode-text {
            font-size: 16px;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .qrcode-desc {
            font-size: 13px;
            color: #888;
            max-width: 200px;
            margin: 0 auto;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #999;
            padding: 15px 0;
            border-top: 1px solid rgba(0,0,0,0.05);
        }
    </style>
</head>
<body>
    <div class="preview-notice">
        这是预览模式，生成的真实文章可能会有所不同
    </div>

    <div class="title-container">
        <h1 class="title">树洞最新消息</h1>
        <p class="subtitle">发布日期：{$this->getCurrentDate()}</p>
    </div>
    
    <div class="intro-card">
        <p>今天又有哪些新鲜事发生在校园里？来树洞看看吧！以下是最新发布的一些有趣内容，点击"查看详情"可直接跳转到小程序查看完整内容和评论。</p>
    </div>
HTML;

        // 添加消息卡片（预览版）
        foreach ($messages as $index => $message) {
            $html .= $this->formatPreviewMessageCardHtml($message, $index);
        }
        
        // 添加小程序码和底部信息
        $qrcodeUrl = $this->serverDomain . '/images/miniprogram_qrcode.jpg';
        
        $html .= <<<HTML
    <div class="qrcode-container">
        <img src="{$qrcodeUrl}" alt="小程序码" style="width: 180px; height: 180px;">
        <p class="qrcode-text">扫码进入树洞小程序</p>
        <p class="qrcode-desc">发现更多校园信息，随时随地参与讨论</p>
    </div>
    
    <div class="footer">
        本文由系统自动生成 · 树洞团队
    </div>
</body>
</html>
HTML;

        return $html;
    }
    
    /**
     * 格式化消息卡片为预览HTML
     * 
     * @param array $message 单条消息数据
     * @param int $index 消息索引
     * @return string HTML格式的消息卡片
     */
    private function formatPreviewMessageCardHtml(array $message, int $index): string
    {
        // 处理用户头像
        $avatarHtml = '<div class="avatar">用</div>';
        if (!empty($message['face_url'])) {
            $avatarHtml = '<img class="avatar" src="' . $message['face_url'] . '" alt="用户头像">';
        }
        
        // 处理用户头衔
        $userTitleHtml = '';
        if (!empty($message['user_title'])) {
            $userTitleHtml = '<span class="user-title">' . htmlspecialchars($message['user_title']) . '</span>';
        }
        
        // 处理图片展示（使用原始URL）
        $imagesHtml = '';
        if (!empty($message['processed_images'])) {
            $imagesCount = count($message['processed_images']);
            
            if ($imagesCount == 1) {
                // 单图模式
                $imageUrl = $message['processed_images'][0];
                $imagesHtml = '<div class="images-container single-image">';
                $imagesHtml .= '<img src="' . $imageUrl . '" alt="消息图片">';
                $imagesHtml .= '</div>';
            } elseif ($imagesCount > 1) {
                // 多图模式，最多显示3张
                $imagesHtml = '<div class="images-container multi-images">';
                
                $displayCount = min(3, $imagesCount);
                for ($i = 0; $i < $displayCount; $i++) {
                    $imageUrl = $message['processed_images'][$i];
                    $moreClass = ($i == 2 && $imagesCount > 3) ? ' more-images" data-count="' . ($imagesCount - 2) : '';
                    $imagesHtml .= '<div class="image-item' . $moreClass . '">';
                    $imagesHtml .= '<img src="' . $imageUrl . '" alt="消息图片">';
                    $imagesHtml .= '</div>';
                }
                
                $imagesHtml .= '</div>';
            }
        }
        
        // 处理统计信息
        $commentCount = $message['comment_count'] ?? 0;
        $likeCount = $message['total_likes'] ?? 0;
        
        return <<<HTML
    <div class="message-card">
        <div class="user-info">
            {$avatarHtml}
            <div>
                <div class="username">{$message['username']}{$userTitleHtml}</div>
            </div>
        </div>
        
        <div class="message-content">
            {$message['content']}
        </div>
        
        {$imagesHtml}
        
        <div class="message-footer">
            <div class="message-stats">
                <span class="comment-count">💬 {$commentCount}</span>
                <span class="like-count" style="margin-left: 15px;">👍 {$likeCount}</span>
            </div>
            <div class="message-time">{$message['formatted_time']}</div>
        </div>
        
        <div style="text-align: right;">
            <a class="view-detail" href="javascript:void(0)">查看详情</a>
        </div>
    </div>
HTML;
    }
    
    /**
     * 创建微信公众号草稿
     * 
     * @param string $title 草稿标题
     * @param string $content 草稿内容（HTML格式）
     * @param string $thumbMediaId 封面图片media_id
     * @param string $author 作者名称
     * @param string $digest 摘要
     * @param string $accessToken 微信AccessToken
     * @param array $messages 可选的消息数组，用于降级处理
     * @return array 创建结果
     */
    private function createWechatDraft(string $title, string $content, string $thumbMediaId, string $author, string $digest, string $accessToken, array $messages = []): array
    {
        try {
            // 记录函数开始执行
            $this->logInfo('开始创建微信公众号图文草稿');
            $this->logInfo('标题: ' . $title);
            $this->logInfo('封面图片media_id: ' . $thumbMediaId);
            
            // 检查封面图片媒体ID是否有效
            if (empty($thumbMediaId)) {
                $this->logError('封面图片media_id为空，无法创建草稿');
                return ['error' => '封面图片media_id为空，无法创建草稿'];
            }
            
            // 确保内容符合微信的要求
            // 1. 移除内容中可能引起问题的特殊字符
            $content = preg_replace('/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/', '', $content);
            
            // 2. 确保内容是有效的UTF-8编码
            if (!mb_check_encoding($content, 'UTF-8')) {
                $content = mb_convert_encoding($content, 'UTF-8', 'auto');
            }
            
            // 3. 确保标题和摘要长度适合
            if (mb_strlen($title, 'UTF-8') > 64) {
                $title = mb_substr($title, 0, 60, 'UTF-8') . '...';
            }
            
            if (mb_strlen($digest, 'UTF-8') > 120) {
                $digest = mb_substr($digest, 0, 117, 'UTF-8') . '...';
            }
            
            // 移除可能存在的base64图片引用
            $content = preg_replace('/src="data:image\/[^;]+;base64,[^"]+"/i', 'src=""', $content);
            
            // 确保内容中的图片引用格式正确 - 不要修改原有格式，维持生成时的样式
            
            // 将内容保存到调试文件以便检查
            file_put_contents(runtime_path() . 'draft_content_debug.html', $content);
            $this->logInfo('已保存草稿内容到调试文件: ' . runtime_path() . 'draft_content_debug.html');
            
            // 记录处理后的内容长度
            $this->logInfo('处理后的内容长度: ' . strlen($content) . ' 字节');
            
            // 构建API请求URL
            $url = "https://api.weixin.qq.com/cgi-bin/draft/add?access_token={$accessToken}";
            
            // 准备要发送的数据
            $data = [
                'articles' => [
                    [
                        'title' => $title,
                        'author' => $author,
                        'digest' => $digest,
                        'content' => $content,
                        'content_source_url' => '',
                        'thumb_media_id' => $thumbMediaId,
                        'need_open_comment' => 0,
                        'only_fans_can_comment' => 0,
                        'show_cover_pic' => 1  // 显示封面图片
                    ]
                ]
            ];
            
            // 输出调试日志，记录请求数据
            $this->logInfo('草稿创建请求数据准备完成');
            
            // 发送POST请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 设置30秒超时时间
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            // 记录响应信息
            $this->logInfo('草稿创建响应状态码: ' . $httpCode);
            
            if ($error) {
                $this->logError('创建草稿CURL错误：' . $error);
                return ['error' => '创建草稿请求失败: ' . $error];
            }
            
            if ($httpCode != 200) {
                $this->logError('创建草稿HTTP错误：状态码 ' . $httpCode);
                return ['error' => '创建草稿HTTP错误：状态码 ' . $httpCode];
            }
            
            // 解析响应
            $result = json_decode($response, true);
            if (!$result) {
                $this->logError('创建草稿响应解析失败：' . $response);
                return ['error' => '创建草稿响应解析失败'];
            }
            
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                $errMsg = $result['errmsg'] ?? '未知错误';
                $errCode = $result['errcode'] ?? 0;
                
                $this->logError("创建草稿失败：错误码 {$errCode}，错误信息 {$errMsg}");
                
                // 特别处理media_id无效的情况
                if ($errCode == 40007 || strpos($errMsg, 'invalid media_id') !== false) { // invalid media_id
                    $this->logWarning('媒体ID无效，尝试重新上传永久素材...');
                    
                    // 再次尝试上传封面图片，但使用不同的方法
                    $this->logInfo('尝试重新上传封面图片');
                    $newThumbMediaId = '';
                    
                    // 查找一个有效的本地封面图片
                    $localCoverPaths = [
                        root_path() . 'public/images/default_cover.jpg',
                        root_path() . 'public/static/images/cover.jpg',
                        root_path() . 'public/uploads/cover.jpg'
                    ];
                    
                    foreach ($localCoverPaths as $path) {
                        if (file_exists($path)) {
                            $this->logInfo('尝试使用本地封面: ' . $path);
                            // 使用不同的上传方法
                            $newThumbMediaId = $this->uploadLocalImageAsPermanentMaterial($path, $accessToken);
                            if (!empty($newThumbMediaId)) {
                                $this->logInfo('成功获取新的封面图片media_id: ' . $newThumbMediaId);
                                break;
                            }
                        }
                    }
                    
                    if (!empty($newThumbMediaId)) {
                        // 创建不包含图片的简化内容
                        $simpleContent = $this->generateSimpleContentWithoutMedia();
                        $this->logInfo('使用简化内容和新的封面图片media_id重新尝试创建草稿');
                        
                        // 尝试创建只包含文本的草稿，使用新的封面图片media_id
                        return $this->createWechatDraft($title, $simpleContent, $newThumbMediaId, $author, $digest, $accessToken);
                    }
                }
                
                // 处理access token过期的情况
                if ($errCode == 40001 || $errCode == 42001 || strpos($errMsg, 'access_token') !== false) {
                    $this->logWarning('AccessToken已过期，尝试刷新...');
                    $newAccessToken = $this->getAccessToken();
                    if (!empty($newAccessToken)) {
                        $this->logInfo('成功获取新的AccessToken，重新尝试创建草稿');
                        return $this->createWechatDraft($title, $content, $thumbMediaId, $author, $digest, $newAccessToken, $messages);
                    }
                }
                
                return ['error' => '创建草稿失败: ' . $errMsg . ' (错误码: ' . $errCode . ')'];
            }
            
            $this->logInfo('草稿创建成功: ' . json_encode($result));
            
            // 返回创建结果
            return $result;
            
        } catch (\Exception $e) {
            $this->logError('创建草稿异常：' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ['error' => '创建草稿异常：' . $e->getMessage()];
        }
    }
    
    /**
     * 创建图片类型的草稿（newspic类型）
     * 
     * @param string $title 草稿标题
     * @param string $imageMediaId 图片media_id
     * @param string $thumbMediaId 封面图片media_id
     * @param string $accessToken 微信AccessToken
     * @return array 创建结果
     */
    private function createWechatDraftAsImageType(string $title, string $imageMediaId, string $thumbMediaId, string $accessToken): array
    {
        try {
            $this->logInfo('开始创建图片类型的草稿');
            
            // 构建API请求URL
            $url = "https://api.weixin.qq.com/cgi-bin/draft/add?access_token={$accessToken}";
            
            // 准备要发送的数据
            $data = [
                'articles' => [
                    [
                        'article_type' => 'newspic', // 图片类型
                        'title' => $title,
                        'content' => '树洞最新消息，查看小程序获取更多信息',
                        'need_open_comment' => 0,
                        'only_fans_can_comment' => 0,
                        'image_info' => [
                            'image_list' => [
                                [
                                    'image_media_id' => $imageMediaId
                                ]
                            ]
                        ],
                        // 这里还需要thumb_media_id
                        'thumb_media_id' => $thumbMediaId
                    ]
                ]
            ];
            
            $this->logInfo('图片类型草稿请求准备完成');
            
            // 发送POST请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $this->logInfo('图片类型草稿创建响应状态码: ' . $httpCode);
            
            if ($error) {
                $this->logError('创建图片类型草稿CURL错误：' . $error);
                return ['error' => '创建草稿请求失败: ' . $error];
            }
            
            // 解析响应
            $result = json_decode($response, true);
            if (!$result) {
                $this->logError('创建图片类型草稿响应解析失败：' . $response);
                return ['error' => '创建草稿响应解析失败'];
            }
            
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                $errMsg = $result['errmsg'] ?? '未知错误';
                $errCode = $result['errcode'] ?? 0;
                $this->logError("创建图片类型草稿失败：错误码 {$errCode}，错误信息 {$errMsg}");
                return ['error' => '创建草稿失败: ' . $errMsg . ' (错误码: ' . $errCode . ')'];
            }
            
            $this->logInfo('图片类型草稿创建成功: ' . json_encode($result));
            return $result;
            
        } catch (\Exception $e) {
            $this->logError('创建图片类型草稿异常：' . $e->getMessage());
            return ['error' => '创建草稿异常：' . $e->getMessage()];
        }
    }
    
    /**
     * 发布微信公众号草稿
     * 
     * @param string $mediaId 草稿的media_id
     * @param string $accessToken 微信AccessToken
     * @return array 发布结果
     */
    private function publishWechatDraft(string $mediaId, string $accessToken): array
    {
        try {
            $this->logInfo('开始发布微信公众号草稿，media_id: ' . $mediaId);
            
            // 构建API请求URL
            $url = "https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token={$accessToken}";
            
            // 准备要发送的数据
            $data = [
                'media_id' => $mediaId
            ];
            
            // 发送POST请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data, JSON_UNESCAPED_UNICODE));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 设置30秒超时
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $this->logInfo('发布草稿响应状态码: ' . $httpCode);
            
            if ($error) {
                $this->logError('发布草稿CURL错误：' . $error);
                return ['error' => '发布草稿请求失败: ' . $error];
            }
            
            if ($httpCode != 200) {
                $this->logError('发布草稿HTTP错误：状态码 ' . $httpCode);
                return ['error' => '发布草稿HTTP错误：状态码 ' . $httpCode];
            }
            
            // 解析响应
            $result = json_decode($response, true);
            if (!$result) {
                $this->logError('发布草稿响应解析失败：' . $response);
                return ['error' => '发布草稿响应解析失败'];
            }
            
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                $errMsg = $result['errmsg'] ?? '未知错误';
                $errCode = $result['errcode'] ?? 0;
                $this->logError('发布草稿失败：错误码 ' . $errCode . '，错误信息 ' . $errMsg);
                
                // 处理access token过期的情况
                if ($errCode == 40001 || $errCode == 42001 || strpos($errMsg, 'access_token') !== false) {
                    $this->logWarning('AccessToken已过期，尝试刷新...');
                    $newAccessToken = $this->getAccessToken();
                    if (!empty($newAccessToken)) {
                        $this->logInfo('成功获取新的AccessToken，重新尝试发布草稿');
                        return $this->publishWechatDraft($mediaId, $newAccessToken);
                    }
                }
                
                return ['error' => $errMsg];
            }
            
            $this->logInfo('发布草稿成功: ' . json_encode($result));
            return $result;
            
        } catch (\Exception $e) {
            $this->logError('发布草稿异常：' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ['error' => '发布草稿异常：' . $e->getMessage()];
        }
    }
    
    /**
     * 获取当前日期格式化字符串
     * 
     * @return string 格式化的日期
     */
    private function getCurrentDate(): string
    {
        return date('Y年m月d日');
    }
    
    /**
     * 显示调试页面，让用户可以直接在界面上生成草稿
     * 
     * @return \think\Response
     */
    public function debugPage()
    {
        // 获取微信配置信息
        $appId = $this->appId;
        $appSecret = substr($this->appSecret, 0, 4) . '****' . substr($this->appSecret, -4);  // 隐藏部分密钥
        
        // 获取封面图片路径
        $defaultCoverImage = $this->serverDomain . $this->coverImagePath;
        
        // 获取最新的错误日志（如果有）
        $errorLog = '';
        $logPath = runtime_path() . 'log/' . date('Ym') . '/' . date('d') . '.log';
        if (file_exists($logPath)) {
            $logContent = file_get_contents($logPath);
            // 提取最后20行
            $lines = explode("\n", $logContent);
            $lines = array_slice($lines, -20);
            $errorLog = implode("\n", $lines);
        }
        
        // 处理错误日志显示内容
        $errorLogDisplay = empty($errorLog) ? '无最近错误日志' : htmlspecialchars($errorLog);
        
        // HTML调试页面
        $html = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>树洞微信公众号草稿生成工具</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, "Microsoft YaHei", sans-serif;
            background-color: #f8f5f2;
            margin: 0;
            padding: 20px;
            color: #5a4a42;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(to right, #7D5A4F, #A67F6D);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .card h2 {
            margin-top: 0;
            color: #7D5A4F;
            font-size: 18px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(to right, #7D5A4F, #A67F6D);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            opacity: 0.9;
        }
        .btn-preview {
            background: #6c757d;
        }
        .result {
            margin-top: 20px;
            display: none;
        }
        .result pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .loading img {
            width: 30px;
            height: 30px;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #6c757d;
            font-size: 12px;
        }
        .default-cover {
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-top: 5px;
            font-size: 12px;
        }
        .config-info {
            color: #6c757d;
            font-style: italic;
            margin-top: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>树洞微信公众号草稿生成工具</h1>
        </div>
        
        <div class="card">
            <h2>公众号配置信息</h2>
            <div class="form-group">
                <label>AppID:</label>
                <input type="text" id="appId" value="{$appId}" readonly>
            </div>
            <div class="form-group">
                <label>AppSecret:</label>
                <input type="text" value="{$appSecret}" readonly>
            </div>
            <p class="config-info">配置文件路径: config/wechat.php</p>
        </div>
        
        <div class="card">
            <h2>草稿生成参数</h2>
            <div class="form-group">
                <label>封面图片URL (可选):</label>
                <input type="text" id="coverImage" placeholder="https://example.com/image.jpg">
                <small style="color:#666">留空将使用默认封面图片</small>
                <div class="default-cover">
                    默认封面图片: <code>{$defaultCoverImage}</code>
                </div>
            </div>
            <div class="form-group">
                <label>最新消息数量:</label>
                <input type="number" id="messageLimit" value="10" min="1" max="20">
            </div>
            <div class="form-group">
                <label>自动发布草稿:</label>
                <select id="autoPublish">
                    <option value="0">否 (仅生成草稿)</option>
                    <option value="1">是 (生成后立即发布)</option>
                </select>
            </div>
        </div>
        
        <div class="card">
            <h2>操作</h2>
            <button id="previewBtn" class="btn-preview">预览生成效果</button>
            <button id="generateBtn">生成草稿</button>
            <div class="loading" id="loading">
                <img src="/static/images/loading.gif" alt="加载中"> 处理中，请稍候...
            </div>
            
            <div class="result" id="result">
                <h3>处理结果:</h3>
                <div id="resultContent"></div>
            </div>
        </div>
        
        <div class="card">
            <h2>日志管理</h2>
            <p>详细的操作日志已保存到独立文件中，点击下面的按钮查看完整日志。</p>
            <a href="/DraftGenerator/viewLog" class="btn" target="_blank" style="background: #17a2b8;">查看当天日志</a>
            <div style="margin-top: 10px; color: #666; font-size: 12px;">
                <strong>日志存放位置:</strong> runtime/logs/draft_generator/{日期}.log
            </div>
        </div>
        
        <div class="footer">
            树洞团队 © {$this->getCurrentDate()}
        </div>
    </div>
    
    <script>
        document.getElementById('previewBtn').addEventListener('click', function() {
            const messageLimit = document.getElementById('messageLimit').value;
            // 在新窗口打开预览页面
            window.open('/DraftGenerator/previewContent?limit=' + messageLimit, '_blank');
        });
        
        document.getElementById('generateBtn').addEventListener('click', function() {
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
            
            // 获取参数
            const coverImage = document.getElementById('coverImage').value;
            const messageLimit = document.getElementById('messageLimit').value;
            const autoPublish = document.getElementById('autoPublish').value;
            
            // 准备请求数据
            const requestData = new FormData();
            if (coverImage) requestData.append('cover_image', coverImage);
            requestData.append('limit', messageLimit);
            requestData.append('auto_publish', autoPublish);
            
            // 发送请求
            fetch('/DraftGenerator/generateDraft', {
                method: 'POST',
                body: requestData
            })
            .then(response => response.json())
            .then(data => {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                
                // 显示结果
                document.getElementById('result').style.display = 'block';
                
                // 根据结果设置样式和内容
                const resultContent = document.getElementById('resultContent');
                if (data.code === 200) {
                    resultContent.innerHTML = '<div class="success">操作成功!</div>';
                    if (data.msg.includes('发布')) {
                        resultContent.innerHTML += '<p>草稿已生成并发布成功。</p>';
                    } else {
                        resultContent.innerHTML += '<p>草稿已生成成功，请前往微信公众平台查看。</p>';
                    }
                    resultContent.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    resultContent.innerHTML = '<div class="error">操作失败!</div>';
                    resultContent.innerHTML += '<p>' + data.msg + '</p>';
                    resultContent.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
            })
            .catch(error => {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                
                // 显示错误
                document.getElementById('result').style.display = 'block';
                document.getElementById('resultContent').innerHTML = 
                    '<div class="error">请求错误!</div>' +
                    '<p>' + error.message + '</p>';
            });
        });
    </script>
</body>
</html>
HTML;

        return response($html);
    }

    /**
     * 显示日志文件内容
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function viewLog(Request $request)
    {
        $date = $request->param('date', date('Y-m-d'));
        
        // 安全检查: 只允许查看日志目录下的文件
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            return response('无效的日期格式', 400);
        }
        
        $logDir = runtime_path() . 'logs/draft_generator/';
        $logFile = $logDir . $date . '.log';
        
        if (!file_exists($logFile)) {
            $html = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看器</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, "Microsoft YaHei", sans-serif;
            background-color: #f8f5f2;
            padding: 20px;
            color: #5a4a42;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        h1 {
            color: #7D5A4F;
            margin-top: 0;
        }
        .alert {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(to right, #7D5A4F, #A67F6D);
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            margin-top: 15px;
        }
        .date-selector {
            margin-bottom: 20px;
        }
        .date-selector input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>日志查看器</h1>
        
        <div class="date-selector">
            <form action="/DraftGenerator/viewLog" method="get">
                <label>选择日期：</label>
                <input type="date" name="date" value="{$date}" max="{$date}">
                <button type="submit" class="btn">查看</button>
            </form>
        </div>
        
        <div class="alert">
            指定日期的日志文件不存在
        </div>
        
        <a href="/DraftGenerator/debugPage" class="btn">返回调试页面</a>
    </div>
</body>
</html>
HTML;
            return response($html);
        }
        
        // 读取日志文件内容
        $logContent = file_get_contents($logFile);
        
        // 处理日志显示，高亮错误和警告信息
        $logContent = htmlspecialchars($logContent);
        $logContent = preg_replace('/\[ERROR\]/', '<span style="color: #dc3545; font-weight: bold;">[ERROR]</span>', $logContent);
        $logContent = preg_replace('/\[WARNING\]/', '<span style="color: #ffc107; font-weight: bold;">[WARNING]</span>', $logContent);
        
        // 显示HTML页面
        $html = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看器 - {$date}</title>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, "Microsoft YaHei", sans-serif;
            background-color: #f8f5f2;
            padding: 20px;
            color: #5a4a42;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        h1 {
            color: #7D5A4F;
            margin-top: 0;
        }
        .log-container {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(to right, #7D5A4F, #A67F6D);
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            margin-top: 15px;
        }
        .date-selector {
            margin-bottom: 20px;
        }
        .date-selector input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>日志查看器 - {$date}</h1>
        
        <div class="date-selector">
            <form action="/DraftGenerator/viewLog" method="get">
                <label>选择日期：</label>
                <input type="date" name="date" value="{$date}" max="{$date}">
                <button type="submit" class="btn">查看</button>
            </form>
        </div>
        
        <div class="log-container">{$logContent}</div>
        
        <a href="/DraftGenerator/debugPage" class="btn">返回调试页面</a>
    </div>
</body>
</html>
HTML;
        
        return response($html);
    }

    /**
     * 生成不包含图片的简化内容，用于处理媒体ID无效的情况
     * 
     * @return string 简化的HTML内容
     */
    private function generateSimpleContentWithoutMedia(): string
    {
        // 创建不包含任何媒体引用的简单HTML
        $html = '<p style="text-align: center; font-weight: bold;">树洞最新消息</p>';
        $html .= '<p style="text-align: center;">' . $this->getCurrentDate() . '</p>';
        $html .= '<p>今天又有哪些新鲜事发生在校园里？来树洞看看吧！最新消息请通过小程序查看。</p>';
        $html .= '<hr>';
        $html .= '<p>由于技术原因，暂时无法显示具体消息内容。请通过扫描小程序码进入树洞小程序查看最新消息。</p>';
        $html .= '<p style="text-align: center; margin-top: 20px;">打开微信搜索"北京高校社区"小程序</p>';
        $html .= '<p style="text-align: center; font-weight: bold;">或直接点击右上角"..."菜单</p>';
        $html .= '<p style="text-align: center;">前往小程序查看更多消息</p>';
        $html .= '<p style="text-align: center; color: #888; font-size: 12px; margin-top: 30px;">本文由系统自动生成 · 树洞团队</p>';
        
        return $html;
    }

    /**
     * 上传小程序码为永久素材
     * 
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回media_id，失败返回空字符串
     */
    private function uploadQRCodeAsPermanentMaterial(string $accessToken): string
    {
        // 尝试多个可能的小程序码路径
        $qrcodePaths = [
            root_path() . 'public/uploads/小程序码.jpg',  // 新路径
            root_path() . 'public/images/xiaochengxu_code.jpg', // 旧路径作为备用
            root_path() . 'public/images/qrcode.jpg',
            root_path() . 'public/static/images/qrcode.png',
            root_path() . 'public/uploads/qrcode.jpg'
        ];
        
        foreach ($qrcodePaths as $qrcodePath) {
            if (file_exists($qrcodePath)) {
                $this->logInfo('找到小程序码图片: ' . $qrcodePath);
                
                // 检查文件扩展名和MIME类型
                $extension = strtolower(pathinfo($qrcodePath, PATHINFO_EXTENSION));
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $qrcodePath);
                finfo_close($finfo);
                
                $this->logInfo('小程序码图片类型: ' . $mimeType . ', 扩展名: ' . $extension);
                
                // 如果文件扩展名不匹配MIME类型，创建一个新的临时文件
                $isTemporary = false;
                $tempPath = $qrcodePath;
                
                if (($mimeType == 'image/jpeg' && $extension != 'jpg' && $extension != 'jpeg') ||
                    ($mimeType == 'image/png' && $extension != 'png') ||
                    ($mimeType == 'image/gif' && $extension != 'gif')) {
                    
                    // 创建正确扩展名的临时文件
                    if ($mimeType == 'image/jpeg') {
                        $newExt = 'jpg';
                    } elseif ($mimeType == 'image/png') {
                        $newExt = 'png';
                    } elseif ($mimeType == 'image/gif') {
                        $newExt = 'gif';
                    } else {
                        $newExt = 'jpg';
                    }
                    
                    $tempPath = tempnam(sys_get_temp_dir(), 'wx_qrcode') . '.' . $newExt;
                    copy($qrcodePath, $tempPath);
                    $isTemporary = true;
                    $this->logInfo('创建带有正确扩展名的临时文件: ' . $tempPath);
                }
                
                // 上传为永久素材
                $mediaId = $this->uploadLocalImageAsPermanentMaterial($tempPath, $accessToken);
                
                // 删除临时文件
                if ($isTemporary && file_exists($tempPath)) {
                    @unlink($tempPath);
                    $this->logInfo('已删除临时文件: ' . $tempPath);
                }
                
                if (!empty($mediaId)) {
                    $this->logInfo('成功上传小程序码，media_id: ' . $mediaId);
                    return $mediaId;
                }
                
                $this->logWarning('上传小程序码失败，尝试下一个路径');
            }
        }
        
        $this->logError('未找到有效的小程序码图片或所有上传尝试均失败');
        return '';
    }

    /**
     * 设置图片处理超时，避免长时间卡住
     * 
     * @param int $seconds 超时秒数
     * @return void
     */
    private function setImageProcessTimeout(int $seconds = 30): void
    {
        // 设置脚本最大执行时间
        ini_set('max_execution_time', $seconds);
        set_time_limit($seconds);
        $this->logInfo("设置图片处理超时时间: {$seconds}秒");
    }

    /**
     * 图片处理前的预处理，设置超时和内存限制
     * 
     * @return void
     */
    private function prepareImageProcessing(): void
    {
        // 设置足够的内存限制
        ini_set('memory_limit', '1024M');
        
        // 设置30秒超时，防止无限期卡住
        $this->setImageProcessTimeout(30);
    }

    private function cropImageToSquare(string $imagePath): ?string
    {
        try {
            // 预处理：设置内存和超时限制
            $this->prepareImageProcessing();
            
            // 检查文件存在
            if (!file_exists($imagePath)) {
                $this->logError('图片文件不存在: ' . $imagePath);
                return null;
            }
            
            // 获取文件大小
            $fileSize = filesize($imagePath);
            $this->logInfo('图片文件大小: ' . $fileSize . ' 字节');
            
            // 根据图片大小选择不同的处理方式
            if ($fileSize > 2 * 1024 * 1024) { // 大于2MB的图片
                $this->logInfo('大图片(>2MB)检测到，使用Imagick或快速处理模式');
                
                // 优先使用Imagick处理大图
                if (extension_loaded('imagick')) {
                    try {
                        $this->logInfo('使用Imagick扩展处理大图片');
                        $imagick = new \Imagick($imagePath);
                        
                        // 获取原始尺寸
                        $origWidth = $imagick->getImageWidth();
                        $origHeight = $imagick->getImageHeight();
                        
                        // 计算裁剪区域
                        $size = min($origWidth, $origHeight);
                        $x = (int)(($origWidth - $size) / 2);
                        $y = (int)(($origHeight - $size) / 2);
                        
                        // 裁剪成正方形
                        $imagick->cropImage($size, $size, $x, $y);
                        
                        // 调整大小到目标尺寸
                        $targetSize = 600;
                        $imagick->resizeImage($targetSize, $targetSize, \Imagick::FILTER_LANCZOS, 1);
                        
                        // 设置压缩质量
                        $imagick->setImageCompressionQuality(75);
                        
                        // 保存结果
                        $output = tempnam(sys_get_temp_dir(), 'squ') . '.' . pathinfo($imagePath, PATHINFO_EXTENSION);
                        $imagick->writeImage($output);
                        $imagick->clear();
                        $imagick->destroy();
                        
                        if (file_exists($output) && filesize($output) > 0) {
                            $this->logInfo('Imagick - 保存成功: ' . $output . ' 大小: ' . filesize($output) . ' 字节');
                            return $output;
                        }
                    } catch (\Exception $e) {
                        $this->logWarning('Imagick处理失败: ' . $e->getMessage() . '，尝试其他方法');
                    }
                }
                
                // 如果Imagick失败，使用快速处理模式
                return $this->fastResizeImage($imagePath);
            } else if ($fileSize > 500 * 1024) { // 500KB-2MB的图片
                // 中等大小图片使用简单处理方法
                $this->logInfo('中等大小图片检测到，使用简单处理方法');
                return $this->simpleSquareImage($imagePath);
            } else {
                // 小图片直接使用简单处理
                $this->logInfo('小图片检测到，使用简单处理方法');
                return $this->simpleSquareImage($imagePath);
            }
            
        } catch (\Exception $e) {
            $this->logError('裁剪图片异常: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 简单的图片处理方法 - 将任何图片处理为方形
     * 
     * @param string $imagePath 图片路径
     * @return string|null 处理后的图片路径，失败则返回null
     */
    private function simpleSquareImage(string $imagePath): ?string 
    {
        try {
            // 设置内存和超时限制
            $this->prepareImageProcessing();
            $this->logInfo('使用简单处理方法: ' . $imagePath);

            // 如果装载了 Imagick，则优先使用
            if (extension_loaded('imagick')) {
                try {
                    $this->logInfo('使用Imagick扩展处理图片');
                    $imagick = new \Imagick($imagePath);
                    
                    // 获取原始尺寸
                    $origWidth = $imagick->getImageWidth();
                    $origHeight = $imagick->getImageHeight();
                    $this->logInfo("Imagick - 原始图片尺寸: {$origWidth}x{$origHeight}");
                    
                    // 计算裁剪区域
                    $size = min($origWidth, $origHeight);
                    $x = (int)(($origWidth - $size) / 2);
                    $y = (int)(($origHeight - $size) / 2);
                    $this->logInfo("Imagick - 裁剪区域: {$x},{$y} 大小: {$size}x{$size}");
                    
                    // 裁剪成正方形
                    $imagick->cropImage($size, $size, $x, $y);
                    
                    // 调整大小到目标尺寸
                    $targetSize = 600;
                    $imagick->resizeImage($targetSize, $targetSize, \Imagick::FILTER_LANCZOS, 1);
                    
                    // 设置压缩质量
                    $imagick->setImageCompressionQuality(80);
                    
                    // 保存结果
                    $output = tempnam(sys_get_temp_dir(), 'squ') . '.' . pathinfo($imagePath, PATHINFO_EXTENSION);
                    $imagick->writeImage($output);
                    $imagick->clear();
                    $imagick->destroy();
                    
                    if (file_exists($output) && filesize($output) > 0) {
                        $this->logInfo('Imagick - 保存成功: ' . $output . ' 大小: ' . filesize($output) . ' 字节');
                        return $output;
                    } else {
                        $this->logWarning('Imagick - 生成图片失败或文件大小为0，尝试GD库');
                    }
                } catch (\Exception $e) {
                    $this->logWarning('Imagick处理失败: ' . $e->getMessage() . '，尝试使用GD库');
                }
            }

            // 获取基本信息
            $info = @getimagesize($imagePath);
            if (!$info) {
                $this->logError('获取图片信息失败: ' . $imagePath);
                return null;
            }
            list($width, $height, $type) = $info;
            $this->logInfo("原始图片尺寸: {$width}x{$height}, 类型: {$type}");

            // 设置目标尺寸
            $targetSize = 600;

            // 计算裁剪区域
            $size = min($width, $height);
            $x = (int)(($width - $size) / 2);
            $y = (int)(($height - $size) / 2);
            $this->logInfo("裁剪区域: {$x},{$y} 大小: {$size}x{$size}");

            // 加载源图像
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = @imagecreatefromjpeg($imagePath);
                    break;
                case IMAGETYPE_PNG:
                    $source = @imagecreatefrompng($imagePath);
                    break;
                case IMAGETYPE_GIF:
                    $source = @imagecreatefromgif($imagePath);
                    break;
                default:
                    $this->logError('不支持的图片类型: ' . $type);
                    return null;
            }
            if (!$source) {
                $this->logError('创建源图像资源失败');
                return null;
            }
            $this->logInfo('源图像加载完成');

            // 使用 imagecrop 进行裁剪
            $this->logInfo('使用 imagecrop 进行裁剪');
            $rect = ['x' => $x, 'y' => $y, 'width' => $size, 'height' => $size];
            $cropped = imagecrop($source, $rect);
            imagedestroy($source);
            if ($cropped === false) {
                $this->logError('imagecrop 裁剪失败');
                return null;
            }
            $this->logInfo('裁剪完成，开始缩放');

            // 使用 imagescale 进行缩放
            $scaled = imagescale($cropped, $targetSize, $targetSize, IMG_BILINEAR_FIXED);
            imagedestroy($cropped);
            if ($scaled === false) {
                $this->logError('imagescale 缩放失败');
                return null;
            }
            $this->logInfo('缩放完成');

            // 保存结果
            $extensions = [IMAGETYPE_JPEG => 'jpg', IMAGETYPE_PNG => 'png', IMAGETYPE_GIF => 'gif'];
            $ext = $extensions[$type] ?? 'jpg';
            $output = tempnam(sys_get_temp_dir(), 'square_') . '.' . $ext;

            switch ($type) {
                case IMAGETYPE_JPEG:
                    $success = imagejpeg($scaled, $output, 80);
                    break;
                case IMAGETYPE_PNG:
                    $success = imagepng($scaled, $output, 5);
                    break;
                case IMAGETYPE_GIF:
                    $success = imagegif($scaled, $output);
                    break;
            }
            imagedestroy($scaled);
            if (empty($success)) {
                $this->logError('保存最终图像失败');
                @unlink($output);
                return null;
            }
            $this->logInfo('保存成功: ' . $output . ' 大小: ' . filesize($output) . ' 字节');
            return $output;
        } catch (\Exception $e) {
            $this->logError('简单处理图片异常: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 快速处理大尺寸图片为方形
     * 使用GD函数库的快速模式，避免处理过程中内存溢出
     * 
     * @param string $imagePath 图片路径
     * @return string|null 处理后的图片路径
     */
    private function fastResizeImage(string $imagePath): ?string
    {
        try {
            // 设置内存和超时限制
            $this->prepareImageProcessing();
            $this->logInfo('开始快速处理图片: ' . $imagePath);
            
            // 检查文件存在
            if (!file_exists($imagePath)) {
                $this->logError('图片文件不存在');
                return null;
            }
            
            // 获取图片类型
            $imageInfo = @getimagesize($imagePath);
            if (!$imageInfo) {
                $this->logError('获取图片信息失败');
                return null;
            }
            
            $width = $imageInfo[0];
            $height = $imageInfo[1];
            $type = $imageInfo[2];
            
            $this->logInfo("快速处理模式 - 原始图片尺寸: {$width}x{$height}, 类型: {$type}");
            
            // 确定输出类型和目标尺寸
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $extension = 'jpg';
                    break;
                case IMAGETYPE_PNG:
                    $extension = 'png';
                    break;
                case IMAGETYPE_GIF:
                    $extension = 'gif';
                    break;
                default:
                    $this->logError('不支持的图片类型: ' . $type);
                    return null;
            }
            
            // 最大处理尺寸 - 直接设置较小的尺寸
            $targetSize = 600; // 600x600像素的方形

            // 如果装载了 Imagick，优先使用 Imagick（处理大图效率更高）
            if (extension_loaded('imagick')) {
                try {
                    $this->logInfo('使用Imagick扩展快速处理大图片');
                    $imagick = new \Imagick($imagePath);
                    
                    // 计算裁剪区域
                    $size = min($width, $height);
                    $x = (int)(($width - $size) / 2);
                    $y = (int)(($height - $size) / 2);
                    
                    // 裁剪成正方形
                    $imagick->cropImage($size, $size, $x, $y);
                    
                    // 调整大小到目标尺寸
                    $imagick->resizeImage($targetSize, $targetSize, \Imagick::FILTER_LANCZOS, 1);
                    
                    // 设置压缩质量
                    $imagick->setImageCompressionQuality(75);
                    
                    // 保存结果
                    $output = tempnam(sys_get_temp_dir(), 'fast_') . '.' . $extension;
                    $imagick->writeImage($output);
                    $imagick->clear();
                    $imagick->destroy();
                    
                    if (file_exists($output) && filesize($output) > 0) {
                        $this->logInfo('Imagick成功处理大图片: ' . $output . ', 大小: ' . filesize($output) . ' 字节');
                        return $output;
                    }
                    
                    $this->logWarning('Imagick处理失败，尝试其他方法');
                } catch (\Exception $e) {
                    $this->logWarning('Imagick处理异常: ' . $e->getMessage());
                }
            }
            
            // 使用系统命令行调用进行处理
            if (function_exists('exec')) {
                // 尝试使用ImageMagick命令行工具
                $outputPath = tempnam(sys_get_temp_dir(), 'fast_') . '.' . $extension;
                
                // 确定裁剪方式（从中心裁剪为正方形，然后缩放）
                $size = min($width, $height);
                $x = ($width - $size) / 2;
                $y = ($height - $size) / 2;
                
                $command = '';
                
                // 根据系统环境选择ImageMagick命令
                if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                    // Windows系统 - 检查是否可用
                    $checkCmd = 'where convert';
                    exec($checkCmd, $output, $returnVar);
                    
                    if ($returnVar === 0) {
                        // ImageMagick可用
                        $command = 'convert "' . $imagePath . '" -crop ' . $size . 'x' . $size . '+' . $x . '+' . $y .
                                   ' -resize ' . $targetSize . 'x' . $targetSize . ' -quality 70 "' . $outputPath . '"';
                    }
                } else {
                    // Linux/Mac系统 - 检查是否可用
                    $checkCmd = 'which convert';
                    exec($checkCmd, $output, $returnVar);
                    
                    if ($returnVar === 0) {
                        // ImageMagick可用
                        $command = 'convert "' . $imagePath . '" -crop ' . $size . 'x' . $size . '+' . $x . '+' . $y .
                                   ' -resize ' . $targetSize . 'x' . $targetSize . ' -quality 70 "' . $outputPath . '"';
                    }
                }
                
                if (!empty($command)) {
                    $this->logInfo('使用ImageMagick处理图片: ' . $command);
                    exec($command, $output, $returnVar);
                    
                    if ($returnVar === 0 && file_exists($outputPath) && filesize($outputPath) > 0) {
                        $this->logInfo('使用ImageMagick处理图片成功: ' . $outputPath . ', 大小: ' . filesize($outputPath) . ' 字节');
                        return $outputPath;
                    }
                    
                    $this->logWarning('ImageMagick处理失败，尝试GD库');
                }
            }
            
            // 如果系统命令失败或不可用，使用普通的GD处理方法，但降低质量和尺寸
            $this->logInfo('使用GD库快速处理大图片');
            
            // 创建一个小的临时图片，先直接加载为小尺寸可以减少内存占用
            $tempImage = imagecreatetruecolor($targetSize, $targetSize);
            
            // 根据图片类型创建源图像，使用低质量模式
            $source = null;
            switch ($type) {
                case IMAGETYPE_JPEG:
                    // 使用低质量加载原图，减少内存占用
                    $source = @imagecreatefromjpeg($imagePath);
                    break;
                case IMAGETYPE_PNG:
                    // 对PNG处理透明度
                    $source = @imagecreatefrompng($imagePath);
                    if ($source) {
                        imagealphablending($tempImage, false);
                        imagesavealpha($tempImage, true);
                        $transparent = imagecolorallocatealpha($tempImage, 255, 255, 255, 127);
                        imagefilledrectangle($tempImage, 0, 0, $targetSize, $targetSize, $transparent);
                    }
                    break;
                case IMAGETYPE_GIF:
                    $source = @imagecreatefromgif($imagePath);
                    break;
            }
            
            if (!$source) {
                $this->logError('无法创建源图像资源');
                imagedestroy($tempImage);
                return null;
            }
            
            // 确定裁剪参数（从中心裁剪）
            $size = min($width, $height);
            $x = ($width - $size) / 2;
            $y = ($height - $size) / 2;
            
            // 一步裁剪并缩放
            if (!imagecopyresampled($tempImage, $source, 0, 0, $x, $y, $targetSize, $targetSize, $size, $size)) {
                $this->logError('图片处理失败');
                imagedestroy($source);
                imagedestroy($tempImage);
                return null;
            }
            
            // 释放原图资源
            imagedestroy($source);
            
            // 保存处理后的图片
            $outputPath = tempnam(sys_get_temp_dir(), 'fast_') . '.' . $extension;
            $success = false;
            
            switch ($type) {
                case IMAGETYPE_JPEG:
                    // 使用较低的质量
                    $success = imagejpeg($tempImage, $outputPath, 60);
                    break;
                case IMAGETYPE_PNG:
                    // 使用较高的压缩比
                    $success = imagepng($tempImage, $outputPath, 6);
                    break;
                case IMAGETYPE_GIF:
                    $success = imagegif($tempImage, $outputPath);
                    break;
            }
            
            // 释放资源
            imagedestroy($tempImage);
            
            if (!$success) {
                $this->logError('保存处理后的图片失败');
                @unlink($outputPath);
                return null;
            }
            
            $this->logInfo('快速处理图片成功: ' . $outputPath . ', 大小: ' . filesize($outputPath) . ' 字节');
            return $outputPath;
            
        } catch (\Exception $e) {
            $this->logError('快速处理图片异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return null;
        }
    }
    
    /**
     * 压缩图片文件到指定大小以下
     * 
     * @param string $imagePath 图片路径
     * @param int $maxSize 最大文件大小（字节）
     * @return string|null 压缩后的图片路径，失败则返回null
     */
    private function compressImageFile(string $imagePath, int $maxSize): ?string
    {
        try {
            $this->logInfo('开始压缩图片: ' . $imagePath . ', 目标大小: ' . $maxSize . ' 字节');
            
            // 检查文件是否存在
            if (!file_exists($imagePath)) {
                $this->logError('图片文件不存在: ' . $imagePath);
                return null;
            }
            
            // 获取图片信息
            $imageInfo = @getimagesize($imagePath);
            if (!$imageInfo) {
                $this->logError('获取图片信息失败: ' . $imagePath);
                return null;
            }
            
            $width = $imageInfo[0];
            $height = $imageInfo[1];
            $type = $imageInfo[2];
            
            // 创建原始图片资源
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = imagecreatefromjpeg($imagePath);
                    $extension = 'jpg';
                    break;
                case IMAGETYPE_PNG:
                    $source = imagecreatefrompng($imagePath);
                    $extension = 'png';
                    break;
                case IMAGETYPE_GIF:
                    $source = imagecreatefromgif($imagePath);
                    $extension = 'gif';
                    break;
                default:
                    $this->logError('不支持的图片类型: ' . $type);
                    return null;
            }
            
            if (!$source) {
                $this->logError('创建图片资源失败');
                return null;
            }
            
            // 计算一个合适的缩放大小，初始缩放比例为0.9
            $scale = 0.9;
            $newWidth = (int)($width * $scale);
            $newHeight = (int)($height * $scale);
            
            // 创建输出文件路径
            $outputPath = tempnam(sys_get_temp_dir(), 'comp_') . '.' . $extension;
            
            // 使用JPEG格式时可以调整质量
            if ($type == IMAGETYPE_JPEG) {
                // 尝试不同的质量级别，从75开始
                $quality = 75;
                
                // 先尝试不缩放，只降低质量
                $scaled = imagecreatetruecolor($width, $height);
                imagecopyresampled($scaled, $source, 0, 0, 0, 0, $width, $height, $width, $height);
                imagejpeg($scaled, $outputPath, $quality);
                imagedestroy($scaled);
                
                // 如果文件仍然太大，尝试进一步降低质量
                if (filesize($outputPath) > $maxSize && $quality > 40) {
                    // 再次尝试更低的质量
                    $quality = 40;
                    $scaled = imagecreatetruecolor($width, $height);
                    imagecopyresampled($scaled, $source, 0, 0, 0, 0, $width, $height, $width, $height);
                    imagejpeg($scaled, $outputPath, $quality);
                    imagedestroy($scaled);
                }
                
                // 如果还是太大，尝试缩小尺寸
                if (filesize($outputPath) > $maxSize) {
                    // 缩小50%并使用较低质量
                    $newWidth = (int)($width * 0.5);
                    $newHeight = (int)($height * 0.5);
                    $scaled = imagecreatetruecolor($newWidth, $newHeight);
                    imagecopyresampled($scaled, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                    imagejpeg($scaled, $outputPath, 50);
                    imagedestroy($scaled);
                }
            } 
            // PNG格式，尝试降低压缩级别
            else if ($type == IMAGETYPE_PNG) {
                // 先尝试压缩级别7（中等压缩）
                $compressionLevel = 7;
                $scaled = imagecreatetruecolor($width, $height);
                
                // 保持透明
                imagealphablending($scaled, false);
                imagesavealpha($scaled, true);
                
                imagecopyresampled($scaled, $source, 0, 0, 0, 0, $width, $height, $width, $height);
                imagepng($scaled, $outputPath, $compressionLevel);
                imagedestroy($scaled);
                
                // 如果文件仍然太大，尝试缩小尺寸
                if (filesize($outputPath) > $maxSize) {
                    $newWidth = (int)($width * 0.7);
                    $newHeight = (int)($height * 0.7);
                    $scaled = imagecreatetruecolor($newWidth, $newHeight);
                    
                    // 保持透明
                    imagealphablending($scaled, false);
                    imagesavealpha($scaled, true);
                    
                    imagecopyresampled($scaled, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                    imagepng($scaled, $outputPath, $compressionLevel);
                    imagedestroy($scaled);
                }
            }
            // GIF格式，只能缩小尺寸
            else if ($type == IMAGETYPE_GIF) {
                $newWidth = (int)($width * 0.7);
                $newHeight = (int)($height * 0.7);
                $scaled = imagecreatetruecolor($newWidth, $newHeight);
                imagecopyresampled($scaled, $source, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
                imagegif($scaled, $outputPath);
                imagedestroy($scaled);
            }
            
            // 释放原图资源
            imagedestroy($source);
            
            $finalSize = filesize($outputPath);
            $this->logInfo('压缩后图片大小: ' . $finalSize . ' 字节');
            
            if ($finalSize <= 0) {
                $this->logError('压缩后图片无效');
                @unlink($outputPath);
                return null;
            }
            
            // 如果文件大小仍然超出限制，但已经尝试过所有方法，仍返回当前结果
            if ($finalSize > $maxSize) {
                $this->logWarning('无法将图片压缩到目标大小，使用当前大小: ' . $finalSize . ' 字节');
            }
            
            return $outputPath;
            
        } catch (\Exception $e) {
            $this->logError('压缩图片异常: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 上传公众号标题图片用于图文消息
     *
     * @param string $accessToken 微信AccessToken
     * @return string 成功返回图片URL，失败返回空字符串
     */
    private function uploadTitleImage(string $accessToken): string
    {
        // 标题图片路径
        $titleImagePath = root_path() . 'public/uploads/公众号标题.jpeg';
        
        // 检查标题图片是否存在
        if (!file_exists($titleImagePath)) {
            $this->logWarning('公众号标题图片不存在：' . $titleImagePath);
            
            // 尝试其他可能的位置
            $alternatePaths = [
                root_path() . 'public/images/公众号标题.jpeg',
                root_path() . 'public/static/images/公众号标题.jpeg',
                root_path() . 'public/公众号标题.jpeg'
            ];
            
            foreach ($alternatePaths as $path) {
                if (file_exists($path)) {
                    $this->logInfo('使用备用标题图片: ' . $path);
                    $titleImagePath = $path;
                    break;
                }
            }
            
            // 如果所有备用路径都不存在，则不包含标题图片
            if (!file_exists($titleImagePath)) {
                $this->logWarning('所有标题图片路径都不存在，将不添加标题图片');
                return '';
            }
        }
        
        // 使用uploadImageForNewsContent上传标题图片，获取URL
        $imageUrl = $this->uploadImageForNewsContent($titleImagePath, $accessToken);
        if (empty($imageUrl)) {
            $this->logError('上传公众号标题图片失败');
        } else {
            $this->logInfo('成功上传公众号标题图片，URL: ' . $imageUrl);
        }
        
        return $imageUrl;
    }
}